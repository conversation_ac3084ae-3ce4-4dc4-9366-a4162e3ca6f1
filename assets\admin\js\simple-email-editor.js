/**
 * Simple Email Template Editor - Consolidated Version
 * Lightweight replacement for Visual Builder
 * Compatible with PHP 8.1-8.4, <PERSON><PERSON> 10
 * Fixes: 405 preview error, duplicate notifications, CSS loading issues
 * Version: 3.0 - Single File Solution
 */

// Global variables
let currentEditor = null;
let editorMode = 'visual'; // 'visual' or 'html'
let isSubmitting = false;
let notificationShown = false;

// Enhanced Configuration
const CONFIG = {
    DEBUG: window.location.hostname === 'localhost' || window.location.search.includes('debug=1'),
    INIT_DELAY: 500,
    RETRY_ATTEMPTS: 3,
    enhanced: true,
    version: '3.0',
    features: {
        preview: true,
        realtime: true,
        validation: true
    }
};

// Set up enhanced editor detection flag
window.SERVER_CONFIG = CONFIG;
window.ENHANCED_EDITOR_LOADED = true;

// Enhanced logging utility
function log(message, type = 'info') {
    if (!CONFIG.DEBUG && type !== 'error') return;
    const prefix = '[EmailEditor-v3.0]';
    const timestamp = new Date().toISOString().substr(11, 8);
    console[type === 'error' ? 'error' : 'log'](`${prefix} [${timestamp}] ${message}`);
}

// Laravel notification integration using existing iziToast system
function showLaravelNotification(message, type = 'success') {
    // Use the existing notify function if available, otherwise fallback to iziToast
    if (typeof notify === 'function') {
        notify(type, message);
    } else if (typeof iziToast !== 'undefined') {
        iziToast[type]({
            message: message,
            position: "topRight"
        });
    } else {
        // Fallback to console if no notification system available
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Add CSS animations
function addNotificationStyles() {
    if (document.getElementById('email-editor-notification-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'email-editor-notification-styles';
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    log('🚀 Simple Email Editor v3.0 Initializing...');
    
    // Add notification styles
    addNotificationStyles();
    
    // Resolve conflicts with nicEdit
    resolveConflicts();
    
    // Post-load verification
    setTimeout(performPostLoadVerification, 2000);
    
    // Check if this is global template page
    if (window.location.pathname.includes('/admin/notification/global')) {
        initializeGlobalTemplateEditor();
    } else {
        initializeSimpleEditor();
    }
    
    // Dispatch enhanced editor ready event
    const event = new CustomEvent('enhancedEditorReady', {
        detail: {
            config: CONFIG,
            timestamp: Date.now()
        }
    });
    document.dispatchEvent(event);
    
    log('✅ Enhanced editor initialization complete');
    
    ensureFormFieldsSync();
});

// Post-load verification function
function performPostLoadVerification() {
    log('🔍 Post-load verification...');
    log('🔍 DOM Elements Check:');
    log('  - Visual Tab:', document.getElementById('visual-tab') ? '✅' : '❌');
    log('  - HTML Tab:', document.getElementById('html-tab') ? '✅' : '❌');
    log('  - Visual Panel:', document.getElementById('visual-editor-panel') ? '✅' : '❌');
    log('  - HTML Panel:', document.getElementById('html-editor-panel') ? '✅' : '❌');
    log('  - HTML Textarea:', document.getElementById('html-editor-textarea') ? '✅' : '❌');
    log('  - Email Body Field:', document.querySelector('input[name="email_body"]') ? '✅' : '❌');
    log('  - Template Form:', document.getElementById('template-form') ? '✅' : '❌');
    
    if (typeof window.SERVER_CONFIG !== 'undefined') {
        log('✅ Enhanced editor loaded successfully');
    } else {
        log('⚠️ Enhanced editor not detected, using fallback', 'warn');
    }
}

// Resolve conflicts with nicEdit and other editors
function resolveConflicts() {
    log('🔧 Resolving editor conflicts...');
    
    // Remove nicEdit from email editor elements
    document.querySelectorAll('textarea[name="email_body"], #email-body, .email-editor-content').forEach(function(el) {
        if (el.classList.contains('nicEdit')) {
            el.classList.remove('nicEdit');
            log(`Removed nicEdit class from element: ${el.id || el.className}`);
        }
    });
    
    // Disable nicEdit globally for email editor elements
    if (typeof window.nicEditors !== 'undefined') {
        const originalAllTextAreas = window.nicEditors.allTextAreas;
        window.nicEditors.allTextAreas = function(config) {
            const textareas = document.getElementsByTagName('textarea');
            const filteredTextareas = [];
            for (let i = 0; i < textareas.length; i++) {
                const textarea = textareas[i];
                if (textarea.name !== 'email_body' && textarea.id !== 'email-body' && !textarea.classList.contains('email-editor-content')) {
                    filteredTextareas.push(textarea);
                }
            }
            return filteredTextareas;
        };
    }
    
    log('✅ Editor conflicts resolved');
}

/**
 * Initialize the simple email editor
 */
function initializeSimpleEditor() {
    log('🚀 Initializing Simple Email Editor with safe function calls...');
    
    // Safe initialization with existence checks
    try {
        // Initialize editor content
        if (typeof initializeEditorContent === 'function') {
            initializeEditorContent();
            log('✅ Editor content initialized');
        } else {
            log('⚠️ initializeEditorContent not found, skipping...', 'warn');
        }
        
        // Initialize editor mode toggle
        if (typeof initializeEditorToggle === 'function') {
            initializeEditorToggle();
            log('✅ Editor mode toggle initialized');
        } else {
            log('⚠️ initializeEditorToggle not found, skipping...', 'warn');
        }

        // Initialize shortcode buttons
        if (typeof initializeShortcodeButtons === 'function') {
            initializeShortcodeButtons();
            log('✅ Shortcode buttons initialized');
        } else {
            log('⚠️ initializeShortcodeButtons not found, skipping...', 'warn');
        }

        // Initialize test email functionality
        if (typeof initializeTestEmail === 'function') {
            initializeTestEmail();
            log('✅ Test email functionality initialized');
        } else {
            log('⚠️ initializeTestEmail not found, skipping...', 'warn');
        }

        // Initialize preview functionality
        if (typeof initializePreview === 'function') {
            initializePreview();
            log('✅ Preview functionality initialized');
        } else {
            log('⚠️ initializePreview not found, skipping...', 'warn');
        }

        // Initialize form submission - most critical component
        if (typeof initializeFormSubmission === 'function') {
            initializeFormSubmission();
            log('✅ Form submission handler initialized');
        } else {
            log('⚠️ initializeFormSubmission not found, skipping...', 'warn');
        }
        
        log('✅ Simple Email Editor Initialized Successfully');
    } catch (error) {
        log(`❌ Error during initialization: ${error.message}`, 'error');
        console.error(error);
    }
}

/**
 * Initialize editor content and ensure proper display
 */
function initializeEditorContent() {
    const visualContent = document.getElementById('visual-editor-content');
    const htmlTextarea = document.getElementById('html-editor-textarea');

    if (visualContent && htmlTextarea) {
        // CRITICAL: Only use content from textarea (single source of truth)
        const initialContent = htmlTextarea.value || '';

        // WINDOWS SERVER FIX: Minimal cleaning to preserve content while preventing duplication
        let cleanedContent = initialContent;

        if (cleanedContent) {
            // Only apply essential Windows Server fixes
            // Remove BOM characters (common on Windows servers)
            cleanedContent = cleanedContent.replace(/^\uFEFF/, '');

            // Normalize line endings (Windows vs Unix)
            cleanedContent = cleanedContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

            // Remove null characters that might cause issues
            cleanedContent = cleanedContent.replace(/\0/g, '');

            // CRITICAL: Only remove exact duplicate lines, not similar content
            // This prevents over-aggressive content removal
            const lines = cleanedContent.split('\n');
            const uniqueLines = [];
            let previousLine = '';

            for (let line of lines) {
                // Only skip if this line is EXACTLY the same as the previous line
                // and it's not an empty line (preserve formatting)
                if (line !== previousLine || line.trim() === '') {
                    uniqueLines.push(line);
                }
                previousLine = line;
            }

            cleanedContent = uniqueLines.join('\n');
        }

        // Apply basic HTML cleaning only (preserve content structure)
        cleanedContent = cleanHtmlContent(cleanedContent);

        // Set content to both editors
        visualContent.innerHTML = cleanedContent;
        htmlTextarea.value = cleanedContent;

        // Add content change listeners
        visualContent.addEventListener('input', function() {
            if (editorMode === 'visual') {
                syncContentToHtml();
                updateHiddenFields();
            }
        });

        // Critical: Add HTML editor change listener for proper saving
        htmlTextarea.addEventListener('input', function() {
            if (editorMode === 'html') {
                syncContentToVisual();
                updateHiddenFields();
            }
        });

        // Add blur event listeners for when user switches focus
        htmlTextarea.addEventListener('blur', function() {
            updateHiddenFields();
            log('🔄 HTML editor blur - content saved');
        });

        visualContent.addEventListener('blur', function() {
            updateHiddenFields();
            log('🔄 Visual editor blur - content saved');
        });

        htmlTextarea.addEventListener('input', function() {
            if (editorMode === 'html') {
                syncContentToVisual();
                updateHiddenFields();
            }
        });

        log('✅ Editor content initialized (Windows Server compatible)');
        log(`Original length: ${initialContent.length}, Cleaned length: ${cleanedContent.length}`);
        log(`Content preview: ${cleanedContent.substring(0, 100)}...`);
    }
}

/**
 * Update hidden form fields with current content
 */
function updateHiddenFields() {
    const currentContent = getCurrentEmailContent();
    const emailBodyInput = document.querySelector('input[name="email_body"]');
    const emailBodyFinalInput = document.querySelector('input[name="email_body_final"]');
    
    if (emailBodyInput) {
        emailBodyInput.value = currentContent;
    }
    if (emailBodyFinalInput) {
        emailBodyFinalInput.value = currentContent;
    }
}

/**
 * Initialize the global template editor
 */
function initializeGlobalTemplateEditor() {
    log('🌐 Initializing Global Template Editor...');
    
    // Initialize editor toggle for global template
    initializeEditorToggle();
    
    // Initialize shortcode buttons
    initializeShortcodeButtons();
    
    // Initialize form submission for global template
    initializeGlobalFormSubmission();
    
    log('✅ Global Template Editor Initialized');
}

/**
 * Initialize editor mode toggle (Visual/HTML)
 */
function initializeEditorToggle() {
    const visualTab = document.getElementById('visual-tab');
    const htmlTab = document.getElementById('html-tab');
    const visualPanel = document.getElementById('visual-editor-panel');
    const htmlPanel = document.getElementById('html-editor-panel');
    
    if (!visualTab || !htmlTab || !visualPanel || !htmlPanel) {
        log('⚠️ Editor toggle elements not found', 'warn');
        return;
    }
    
    // Visual tab click handler
    visualTab.addEventListener('click', function(e) {
        e.preventDefault();
        switchToVisualMode();
    });
    
    // HTML tab click handler
    htmlTab.addEventListener('click', function(e) {
        e.preventDefault();
        switchToHtmlMode();
    });
    
    // Initialize in visual mode
    switchToVisualMode();
    
    log('✅ Editor toggle initialized');
}

/**
 * Switch to visual editing mode
 */
function switchToVisualMode() {
    editorMode = 'visual';
    
    const visualTab = document.getElementById('visual-tab');
    const htmlTab = document.getElementById('html-tab');
    const visualPanel = document.getElementById('visual-editor-panel');
    const htmlPanel = document.getElementById('html-editor-panel');
    
    if (visualTab && htmlTab && visualPanel && htmlPanel) {
        // Update tab states
        visualTab.classList.add('active');
        htmlTab.classList.remove('active');
        
        // Update panel visibility
        visualPanel.style.display = 'block';
        htmlPanel.style.display = 'none';
        
        // Sync content from HTML to Visual if needed
        syncContentToVisual();
        
        log('📝 Switched to Visual mode');
    }
}

/**
 * Switch to HTML editing mode
 */
function switchToHtmlMode() {
    editorMode = 'html';
    
    const visualTab = document.getElementById('visual-tab');
    const htmlTab = document.getElementById('html-tab');
    const visualPanel = document.getElementById('visual-editor-panel');
    const htmlPanel = document.getElementById('html-editor-panel');
    
    if (visualTab && htmlTab && visualPanel && htmlPanel) {
        // Update tab states
        visualTab.classList.remove('active');
        htmlTab.classList.add('active');
        
        // Update panel visibility
        visualPanel.style.display = 'none';
        htmlPanel.style.display = 'block';
        
        // Sync content from Visual to HTML
        syncContentToHtml();
        
        log('🔧 Switched to HTML mode');
    }
}

/**
 * Sync content from HTML editor to visual editor
 */
function syncContentToVisual() {
    const htmlTextarea = document.getElementById('html-editor-textarea');
    const visualContent = document.getElementById('visual-editor-content');

    if (htmlTextarea && visualContent) {
        const htmlContent = htmlTextarea.value;
        const cleanedContent = cleanHtmlContent(htmlContent);

        // Set the content directly to preserve all styling
        visualContent.innerHTML = cleanedContent;

        // Apply additional email template styles to ensure proper rendering
        applyEmailTemplateStyles(visualContent);

        // Update all hidden fields immediately
        updateHiddenFields();

        log('🔄 Content synced from HTML to Visual editor');
        log(`Content length: ${cleanedContent.length}`);
    }
}

/**
 * Sync content from visual editor to HTML editor
 */
function syncContentToHtml() {
    const htmlTextarea = document.getElementById('html-editor-textarea');
    const visualContent = document.getElementById('visual-editor-content');

    if (htmlTextarea && visualContent) {
        // Get clean HTML content from visual editor
        let cleanContent = cleanHtmlContent(visualContent.innerHTML);
        htmlTextarea.value = cleanContent;

        // Update all hidden fields immediately
        updateHiddenFields();

        log('🔄 Synced content to HTML editor');
    }
}

/**
 * Clean HTML content for email templates - PRESERVE ALL STYLING AND STRUCTURE
 */
function cleanHtmlContent(html) {
    if (!html) return '';
    
    let cleaned = html;
    
    // ULTRA-MINIMAL CLEANING - Only remove script tags that could break email clients
    // Remove only script tags (but preserve everything else including styles, tables, etc.)
    cleaned = cleaned.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
    
    // Remove only external stylesheet links (preserve inline styles and all other HTML)
    cleaned = cleaned.replace(/<link[^>]*rel=["']stylesheet["'][^>]*href=["'][^"']*["'][^>]*>/gi, '');
    
    // Preserve all other HTML structure, inline styles, tables, divs, etc.
    // Only trim excessive whitespace at the beginning and end
    cleaned = cleaned.trim();
    
    // Log the cleaning process for debugging
    log(`🧹 Content preserved - Original: ${html.length} chars, Cleaned: ${cleaned.length} chars`);
    
    return cleaned;
}

/**
 * Apply email template styles to ensure proper rendering
 */
function applyEmailTemplateStyles(container) {
    if (!container) return;
    
    // Apply base container styling
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.lineHeight = '1.6';
    container.style.color = '#333333';
    container.style.backgroundColor = '#ffffff';
    container.style.padding = '20px';
    container.style.border = '1px solid #e0e0e0';
    container.style.borderRadius = '8px';
    container.style.minHeight = '400px';
    
    // Ensure all tables have proper email styling
    const tables = container.querySelectorAll('table');
    tables.forEach(table => {
        table.style.width = '100%';
        table.style.borderCollapse = 'collapse';
        table.style.fontFamily = 'Arial, sans-serif';
        table.style.margin = '0';
        table.style.padding = '0';
    });
    
    // Style table cells
    const cells = container.querySelectorAll('td, th');
    cells.forEach(cell => {
        cell.style.fontFamily = 'Arial, sans-serif';
        cell.style.verticalAlign = 'top';
        if (!cell.style.padding) {
            cell.style.padding = '10px';
        }
    });
    
    // Ensure all images are responsive
    const images = container.querySelectorAll('img');
    images.forEach(img => {
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
        img.style.display = 'block';
        img.style.border = '0';
        img.style.outline = 'none';
    });
    
    // Style links
    const links = container.querySelectorAll('a');
    links.forEach(link => {
        link.style.color = '#dc3545';
        link.style.textDecoration = 'none';
    });
    
    // Style headings
    const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
        heading.style.fontFamily = 'Arial, sans-serif';
        heading.style.margin = '20px 0 10px 0';
        heading.style.color = '#333333';
    });
    
    // Style paragraphs
    const paragraphs = container.querySelectorAll('p');
    paragraphs.forEach(p => {
        p.style.fontFamily = 'Arial, sans-serif';
        p.style.margin = '10px 0';
        p.style.lineHeight = '1.6';
    });
    
    // Style divs
    const divs = container.querySelectorAll('div');
    divs.forEach(div => {
        div.style.fontFamily = 'Arial, sans-serif';
    });
    
    // Apply any inline styles that might be in style attributes
    const elementsWithStyle = container.querySelectorAll('[style]');
    elementsWithStyle.forEach(el => {
        // Preserve existing styles but ensure font family is set
        if (!el.style.fontFamily) {
            el.style.fontFamily = 'Arial, sans-serif';
        }
    });
    
    log('✅ Applied comprehensive email template styles');
}

/**
 * Initialize shortcode buttons
 */
function initializeShortcodeButtons() {
    const shortcodeButtons = document.querySelectorAll('.shortcode-btn');
    
    shortcodeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const shortcode = this.getAttribute('data-shortcode');
            if (shortcode) {
                insertShortcode(shortcode);
            }
        });
    });
    
    log(`✅ Initialized ${shortcodeButtons.length} shortcode buttons`);
}

/**
 * Insert shortcode into the active editor
 */
function insertShortcode(shortcode) {
    if (editorMode === 'visual') {
        const visualContent = document.getElementById('visual-editor-content');
        if (visualContent) {
            // Insert at cursor position or append
            const selection = window.getSelection();
            if (selection.rangeCount > 0 && visualContent.contains(selection.anchorNode)) {
                const range = selection.getRangeAt(0);
                range.deleteContents();
                range.insertNode(document.createTextNode(shortcode));
            } else {
                visualContent.innerHTML += shortcode;
            }
            syncContentToHtml();
        }
    } else {
        const htmlTextarea = document.getElementById('html-editor-textarea');
        if (htmlTextarea) {
            const cursorPos = htmlTextarea.selectionStart;
            const textBefore = htmlTextarea.value.substring(0, cursorPos);
            const textAfter = htmlTextarea.value.substring(htmlTextarea.selectionEnd);
            htmlTextarea.value = textBefore + shortcode + textAfter;
            htmlTextarea.selectionStart = htmlTextarea.selectionEnd = cursorPos + shortcode.length;
            htmlTextarea.focus();
        }
    }
    
    log(`📝 Inserted shortcode: ${shortcode}`);
}

/**
 * Initialize test email functionality
 */
function initializeTestEmail() {
    const sendTestEmailBtn = document.getElementById('send-test-email');
    
    if (sendTestEmailBtn) {
        sendTestEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            sendTestEmail();
            log('📧 Test email button clicked');
        });
    } else {
        log('⚠️ Send test email button not found', 'warn');
    }
    
    log('✅ Test email functionality initialized');
}

/**
 * Send test email
 */
function sendTestEmail() {
    const testEmailInput = document.getElementById('test-email-address');
    const testEmail = testEmailInput?.value;
    const resultDiv = document.getElementById('test-email-result');
    
    if (!testEmail || !testEmail.trim()) {
        showLaravelNotification('Please enter a valid email address', 'error');
        return;
    }
    
    // Show loading state
    const sendBtn = document.getElementById('send-test-email');
    if (sendBtn) {
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="las la-spinner la-spin"></i> Sending...';
    }
    
    if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '<div class="alert alert-info"><i class="las la-spinner la-spin"></i> Sending test email...</div>';
    }
    
    // Sync content before sending
    ensureFormFieldsSync();
    
    const formData = new FormData();
    formData.append('test_email', testEmail);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '');
    
    // Add current email content
    const emailBody = getCurrentEmailContent();
    formData.append('email_body', emailBody);
    
    // Use the correct test email route from window.templateData
    const testEmailRoute = window.templateData?.testEmailRoute || window.location.href.replace('/edit/', '/test-email/');
    
    fetch(testEmailRoute, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        // Reset button state
        if (sendBtn) {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="las la-paper-plane"></i> Send';
        }
        
        if (data.success) {
            showLaravelNotification('Test email sent successfully!', 'success');
            if (resultDiv) {
                resultDiv.innerHTML = '<div class="alert alert-success"><i class="las la-check"></i> Test email sent successfully to ' + testEmail + '</div>';
            }
        } else {
            showLaravelNotification(data.message || 'Failed to send test email', 'error');
            if (resultDiv) {
                resultDiv.innerHTML = '<div class="alert alert-danger"><i class="las la-times"></i> ' + (data.message || 'Failed to send test email') + '</div>';
            }
        }
    })
    .catch(error => {
        log('Test email error: ' + error.message, 'error');
        showLaravelNotification('Error sending test email', 'error');
        
        // Reset button state
        if (sendBtn) {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="las la-paper-plane"></i> Send';
        }
        
        if (resultDiv) {
            resultDiv.innerHTML = '<div class="alert alert-danger"><i class="las la-times"></i> Error sending test email</div>';
        }
    });
}

/**
 * Initialize preview functionality
 */
function initializePreview() {
    const previewBtn = document.getElementById('preview-email');
    
    if (previewBtn) {
        previewBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openPreview();
        });
        log('✅ Preview button event listener attached');
    } else {
        log('⚠️ Preview button not found', 'warn');
    }
    
    log('✅ Preview functionality initialized');
}

/**
 * Open email preview
 */
function openPreview() {
    try {
        // Sync content before preview
        ensureFormFieldsSync();
        
        const emailBody = getCurrentEmailContent();
        const subject = document.querySelector('input[name="subject"]')?.value || 'Email Preview';
        
        if (!emailBody || !emailBody.trim()) {
            showLaravelNotification('No content to preview', 'warning');
            return;
        }
        
        // Create preview window with proper email styling
        const previewWindow = window.open('', 'emailPreview', 'width=900,height=700,scrollbars=yes,resizable=yes,location=no,menubar=no,toolbar=no');
        
        if (previewWindow) {
            const previewHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${subject}</title>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            margin: 0; 
                            padding: 20px;
                            background: #f8f9fa;
                            line-height: 1.6;
                        }
                        .preview-header {
                            background: #343a40;
                            color: white;
                            padding: 15px 20px;
                            margin: -20px -20px 20px -20px;
                            border-radius: 8px 8px 0 0;
                        }
                        .preview-header h3 {
                            margin: 0;
                            font-size: 18px;
                        }
                        .preview-container {
                            max-width: 800px;
                            margin: 0 auto;
                            background: white;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                            overflow: hidden;
                        }
                        .preview-content { 
                            padding: 20px;
                            background: white;
                            font-family: Arial, sans-serif;
                            line-height: 1.6;
                            color: #333;
                        }
                        .preview-content table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 0;
                            font-family: Arial, sans-serif;
                        }
                        .preview-content td, .preview-content th {
                            padding: 10px;
                            vertical-align: top;
                            font-family: Arial, sans-serif;
                        }
                        .preview-content img {
                            max-width: 100%;
                            height: auto;
                            display: block;
                            border: 0;
                        }
                        .preview-content a {
                            color: #dc3545;
                            text-decoration: none;
                        }
                        .preview-content h1, .preview-content h2, .preview-content h3,
                        .preview-content h4, .preview-content h5, .preview-content h6 {
                            font-family: Arial, sans-serif;
                            margin: 20px 0 10px 0;
                            color: #333;
                        }
                        .preview-content p {
                            font-family: Arial, sans-serif;
                            margin: 10px 0;
                            line-height: 1.6;
                        }
                    </style>
                </head>
                <body>
                    <div class="preview-container">
                        <div class="preview-header">
                            <h3>📧 Email Preview: ${subject}</h3>
                        </div>
                        <div class="preview-content">
                            ${emailBody}
                        </div>
                    </div>
                </body>
                </html>
            `;
            
            previewWindow.document.write(previewHTML);
            previewWindow.document.close();
            previewWindow.focus();
            
            log('👁️ Enhanced preview opened successfully');
            showLaravelNotification('Preview opened in new window', 'success');
        } else {
            // Fallback: show preview in modal if popup is blocked
            showPreviewModal(emailBody, subject);
        }
    } catch (error) {
        log('❌ Preview error: ' + error.message, 'error');
        showLaravelNotification('Error opening preview: ' + error.message, 'error');
    }
}

/**
 * Show preview in modal as fallback
 */
function showPreviewModal(emailBody, subject) {
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
    `;
    
    // Create modal content
    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        width: 90%;
        max-width: 800px;
        height: 80%;
        border-radius: 8px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    `;
    
    // Create modal header
    const modalHeader = document.createElement('div');
    modalHeader.style.cssText = `
        background: #343a40;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    `;
    modalHeader.innerHTML = `
        <h3 style="margin: 0; font-size: 18px;">📧 Email Preview: ${subject}</h3>
        <button onclick="this.closest('.modal-overlay').remove()" style="
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
        ">×</button>
    `;
    
    // Create modal body
    const modalBody = document.createElement('div');
    modalBody.style.cssText = `
        flex: 1;
        overflow: auto;
        padding: 20px;
        font-family: Arial, sans-serif;
        line-height: 1.6;
    `;
    modalBody.innerHTML = emailBody;
    
    modalContent.appendChild(modalHeader);
    modalContent.appendChild(modalBody);
    modalOverlay.appendChild(modalContent);
    modalOverlay.className = 'modal-overlay';
    
    // Close modal on overlay click
    modalOverlay.addEventListener('click', function(e) {
        if (e.target === modalOverlay) {
            modalOverlay.remove();
        }
    });
    
    document.body.appendChild(modalOverlay);
    showLaravelNotification('Preview opened in modal (popup blocked)', 'info');
}

/**
 * Get current email content from active editor
 */
function getCurrentEmailContent() {
    if (editorMode === 'visual') {
        const visualContent = document.getElementById('visual-editor-content');
        return visualContent ? visualContent.innerHTML : '';
    } else {
        const htmlTextarea = document.getElementById('html-editor-textarea');
        return htmlTextarea ? htmlTextarea.value : '';
    }
}

/**
 * Ensure form fields are synchronized before submission - PRESERVE ORIGINAL STYLING
 */
function ensureFormFieldsSync() {
    const emailBodyField = document.querySelector('input[name="email_body"]') || document.querySelector('textarea[name="email_body"]');
    const hiddenField = document.getElementById('email_body_final');
    const htmlEditor = document.getElementById('html-editor-textarea');
    // If email_body field exists but hidden field doesn't, create it
    if (emailBodyField && !hiddenField) {
        const newHiddenField = document.createElement('input');
        newHiddenField.type = 'hidden';
        newHiddenField.id = 'email_body_final';
        newHiddenField.name = 'email_body_final';
        newHiddenField.value = emailBodyField.value;
        emailBodyField.parentNode.appendChild(newHiddenField);
        console.log('✅ Created missing email_body_final field');
    }
    // Sync content from main field to editor
    if (emailBodyField && htmlEditor) {
        htmlEditor.value = emailBodyField.value;
        console.log('✅ Synced content from email_body to HTML editor');
    }
    // Initial sync to ensure all fields are aligned
    syncEditorContent();
}

function syncEditorContent() {
    const visualEditor = document.getElementById('visual-editor-content');
    const htmlEditor = document.getElementById('html-editor-textarea');
    const hiddenField = document.getElementById('email_body_final');
    const emailBodyField = document.querySelector('input[name="email_body"]') || document.querySelector('textarea[name="email_body"]');
    let content = '';

    // CRITICAL FIX: Always get content from HTML editor as it's the source of truth
    if (htmlEditor && htmlEditor.value.trim()) {
        content = htmlEditor.value;
        log('📝 Using HTML editor content for save');
    } else if (visualEditor && visualEditor.innerHTML.trim()) {
        content = visualEditor.innerHTML;
        // Sync to HTML editor immediately
        if (htmlEditor) {
            htmlEditor.value = content;
        }
        log('📝 Using Visual editor content for save');
    }

    // WINDOWS SERVER FIX: Minimal cleaning to preserve content
    if (content) {
        // Only apply essential fixes to prevent corruption
        // Remove BOM characters that might be added by Windows servers
        content = content.replace(/^\uFEFF/, '');

        // Normalize line endings for Windows compatibility
        content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

        // Remove any null characters that might cause issues
        content = content.replace(/\0/g, '');

        // Trim only leading/trailing whitespace (preserve internal formatting)
        content = content.trim();

        // CRITICAL: Only remove exact consecutive duplicate lines
        // This is much less aggressive than the previous approach
        const lines = content.split('\n');
        const uniqueLines = [];
        let previousLine = '';

        for (let line of lines) {
            // Only skip if this line is EXACTLY the same as the previous line
            // Keep all content, just remove exact duplicates
            if (line !== previousLine || line.trim() === '') {
                uniqueLines.push(line);
            }
            previousLine = line;
        }

        content = uniqueLines.join('\n');
    }

    // Only set (never append/merge) the content
    if (hiddenField) {
        hiddenField.value = content;
        console.log('✅ Set email_body_final, length:', content.length);
    }
    if (emailBodyField) {
        emailBodyField.value = content;
        console.log('✅ Set email_body, length:', content.length);
    }

    // WINDOWS SERVER DEBUG: Log content info for debugging
    console.log('SYNCED CONTENT - Length:', content.length, 'Preview:', content.substring(0, 100));
}

/**
 * Initialize form submission for regular templates
 */
function initializeFormSubmission() {
    const form = document.getElementById('template-form') || document.querySelector('form');
    
    if (!form) {
        log('⚠️ Template form not found', 'warn');
        return;
    }
    
    // Prevent multiple event listeners
    if (form.hasAttribute('data-email-editor-initialized')) return;
    form.setAttribute('data-email-editor-initialized', 'true');
    
    form.addEventListener('submit', function(e) {
        log('📝 Form submission detected, syncing content...');
        
        if (isSubmitting) {
            e.preventDefault();
            return false;
        }
        
        isSubmitting = true;
        
        try {
            // Add CSRF token if missing
            if (!form.querySelector('input[name="_token"]')) {
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                if (csrfToken) {
                    const tokenInput = document.createElement('input');
                    tokenInput.type = 'hidden';
                    tokenInput.name = '_token';
                    tokenInput.value = csrfToken;
                    form.appendChild(tokenInput);
                }
            }
            
            // Validate required fields
            const subject = form.querySelector('input[name="subject"]')?.value;
            
            if (!subject || !subject.trim()) {
                e.preventDefault();
                showLaravelNotification('Subject is required', 'error');
                isSubmitting = false;
                return false;
            }
            
            // Sync editor content before submission and clean it
            ensureFormFieldsSync();
            
            const emailContent = getCurrentEmailContent();
            const cleanedContent = cleanHtmlContent(emailContent);
            
            if (!cleanedContent || !cleanedContent.trim()) {
                e.preventDefault();
                showLaravelNotification('Email content is required', 'error');
                isSubmitting = false;
                return false;
            }
            
            // Enhanced field detection with multiple selectors
            let emailBodyInput = form.querySelector('input[name="email_body"]');  // Hidden input
            if (!emailBodyInput) {
                emailBodyInput = form.querySelector('textarea[name="email_body"]');  // Textarea fallback
            }
            if (!emailBodyInput) {
                emailBodyInput = document.getElementById('email_body');  // ID fallback
            }
            
            let emailBodyFinalInput = form.querySelector('input[name="email_body_final"]');
            if (!emailBodyFinalInput) {
                emailBodyFinalInput = document.getElementById('email_body_final');
            }
            
            // Create missing fields if needed
            if (!emailBodyInput) {
                emailBodyInput = document.createElement('input');
                emailBodyInput.type = 'hidden';
                emailBodyInput.name = 'email_body';
                form.appendChild(emailBodyInput);
                log('✅ Created missing email_body field for form submission');
            }
            
            if (!emailBodyFinalInput) {
                emailBodyFinalInput = document.createElement('input');
                emailBodyFinalInput.type = 'hidden';
                emailBodyFinalInput.name = 'email_body_final';
                form.appendChild(emailBodyFinalInput);
                log('✅ Created missing email_body_final field for form submission');
            }
            
            // Update form fields with cleaned content
            if (emailBodyInput) {
                emailBodyInput.value = cleanedContent;
                log('✅ Updated email_body field for submission, length: ' + cleanedContent.length);
            }
            if (emailBodyFinalInput) {
                emailBodyFinalInput.value = cleanedContent;
                log('✅ Updated email_body_final field for submission');
            }
            
            // Also update the textarea to reflect cleaned content
            const htmlTextarea = document.getElementById('html-editor-textarea');
            if (htmlTextarea) {
                htmlTextarea.value = cleanedContent;
            }
            
            log('📤 Form submission started');
            log(`Subject: ${subject}`);
            log(`Email content length: ${cleanedContent.length}`);
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                
                // Reset button after 10 seconds (increased timeout)
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                    isSubmitting = false;
                }, 10000);
            }
            
            // Show saving notification
            showLaravelNotification('Saving template...', 'info');
            
            // Allow form to submit normally
            return true;
            
        } catch (error) {
            log('❌ Form submission error: ' + error.message, 'error');
            e.preventDefault();
            isSubmitting = false;
            showLaravelNotification('Error preparing form submission', 'error');
            return false;
        }
    });
    
    log('✅ Form submission initialized');
}

/**
 * Initialize form submission for global templates
 */
function initializeGlobalFormSubmission() {
    const form = document.getElementById('global-template-form');
    
    if (!form) {
        log('⚠️ Global template form not found', 'warn');
        return;
    }
    
    form.addEventListener('submit', function(e) {
        if (isSubmitting) {
            e.preventDefault();
            return false;
        }
        
        isSubmitting = true;
        
        // Sync content before submission
        ensureFormFieldsSync();
        
        log('📤 Global template form submission started');
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
            
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Update Global Template';
                isSubmitting = false;
            }, 5000);
        }
        
        showLaravelNotification('Updating global template...', 'info');
    });
    
    log('✅ Global form submission initialized');
}

// Export for global access
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        log,
        showLaravelNotification,
        initializeSimpleEditor,
        initializeGlobalTemplateEditor
    };
}

// Global functions for external access
window.EmailEditor = {
    log,
    showLaravelNotification,
    switchToVisualMode,
    switchToHtmlMode,
    insertShortcode,
    openPreview,
    ensureFormFieldsSync,
    getCurrentEmailContent
};
