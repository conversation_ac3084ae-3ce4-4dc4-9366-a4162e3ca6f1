@extends('admin.layouts.app')
@section('panel')
    <div class="row">
        <div class="col-md-12">
            <!-- <div class="card overflow-hidden">
                <div class="card-body p-0">
                    <div class="table-responsive table-responsive--sm">
                        <table class="table align-items-center table--light">
                            <thead>
                            <tr>
                                <th>@lang('Short Code')</th>
                                <th>@lang('Description')</th>
                            </tr>
                            </thead>
                            <tbody class="list">
                                @php
                                    $shortcodeArray = $template->shortcodes_array ?? [];
                                @endphp
                                @forelse($shortcodeArray as $shortcode => $key)
                                <tr>
                                    {{-- blade-formatter-disable --}}
                                    <th><span class="short-codes">@php echo "{{". $shortcode ."}}"; @endphp</span></th>
                                    {{-- blade-formatter-enable --}}
                                    <td>{{ __($key) }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="100%" class="text-muted text-center">{{ __($emptyMessage) }}</td>
                                </tr>
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>card end -->

            <!-- <h6 class="mt-4 mb-2">@lang('Global Short Codes')</h6>
            <div class="card overflow-hidden">
                <div class="card-body p-0">
                    <div class="table-responsive table-responsive--sm">
                        <table class=" table align-items-center table--light">
                            <thead>
                            <tr>
                                <th>@lang('Short Code') </th>
                                <th>@lang('Description')</th>
                            </tr>
                            </thead>
                            <tbody class="list">
                            @foreach(($general->global_shortcodes ?? []) as $shortCode => $codeDetails)
                            <tr>
                                {{-- blade-formatter-disable --}}
                                <td><span class="short-codes">@{{@php echo $shortCode; @endphp}}</span></td>
                                {{-- blade-formatter-enable --}}
                                <td>{{ __($codeDetails) }}</td>
                            </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div> -->

        </div>
    </div>

    <form id="template-form" action="{{ route('admin.setting.notification.template.update',$template->id) }}" method="post">
        @csrf

        <!-- TOP SECTION: Email Template Editor (Full Width) -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg--primary">
                        <h5 class="card-title text-white">
                            <i class="las la-envelope"></i> @lang('Email Template Editor')
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Email Subject and Status Row -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('Email Subject') <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" placeholder="@lang('Enter email subject')" name="subject" value="{{ $template->subj }}" required/>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>@lang('Email Status') <span class="text-danger">*</span></label>
                                    <input type="checkbox" data-height="46px" data-width="100%" data-onstyle="-success"
                                       data-offstyle="-danger" data-bs-toggle="toggle" data-on="@lang('Send Email')"
                                       data-off="@lang("Don't Send")" name="email_status"
                                       @if($template->email_status) checked @endif>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>@lang('Test Email') <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="email" id="test-email-address" class="form-control"
                                               placeholder="<EMAIL>" value="<EMAIL>">
                                        <button type="button" id="send-test-email" class="btn btn--success">
                                            <i class="las la-paper-plane"></i> @lang('Send')
                                        </button>
                                    </div>
                                    <div id="test-email-result" class="mt-2" style="display: none;"></div>
                                </div>
                            </div>
                        </div>

                        <!-- SIMPLIFIED EMAIL EDITOR -->
                        <div class="form-group">
                            <label>@lang('Message') <span class="text-danger">*</span></label>

                            <div class="simple-email-editor mt-3">
                                <!-- Shortcode Buttons Bar -->
                                <div class="shortcode-buttons-bar">
                                    <span class="shortcode-label">@lang('Shortcodes'):</span>
                                    @php
                                        // Get template-specific shortcodes
                                        $templateShortcodes = [];
                                        if ($template->shortcodes) {
                                            $shortcodeData = is_string($template->shortcodes) ? json_decode($template->shortcodes, true) : (array)$template->shortcodes;
                                            foreach ($shortcodeData as $key => $description) {
                                                $templateShortcodes[$key] = $description;
                                            }
                                        }

                                        // Common shortcodes with icons
                                        $commonShortcodes = [
                                            'fullname' => 'Full name of the user',
                                            'username' => 'Username of the user',
                                            'email' => 'Email address of the user',
                                            'site_name' => 'Name of the website',
                                            'site_url' => 'Website URL',
                                            'amount' => 'Transaction amount',
                                            'currency' => 'Currency symbol',
                                            'balance' => 'Account balance',
                                            'transaction_id' => 'Transaction ID',
                                            'code' => 'Verification code',
                                            'message' => 'Custom message content'
                                        ];

                                        // Define icons for different shortcode types
                                        $shortcodeIcons = [
                                            'fullname' => 'las la-user',
                                            'username' => 'las la-at',
                                            'email' => 'las la-envelope',
                                            'site_name' => 'las la-globe',
                                            'site_url' => 'las la-link',
                                            'amount' => 'las la-dollar-sign',
                                            'currency' => 'las la-coins',
                                            'mt5_login' => 'las la-chart-line',
                                            'mt5_group' => 'las la-layer-group',
                                            'leverage' => 'las la-balance-scale',
                                            'balance' => 'las la-wallet',
                                            'new_balance' => 'las la-wallet',
                                            'transaction_id' => 'las la-receipt',
                                            'transaction_date' => 'las la-calendar',
                                            'code' => 'las la-key',
                                            'reason' => 'las la-comment',
                                            'ib_type' => 'las la-users',
                                            'referral_code' => 'las la-share-alt',
                                            'server_name' => 'las la-server',
                                            'message' => 'las la-comment-dots'
                                        ];

                                        // Merge template-specific and common shortcodes (limit to most important ones)
                                        $allShortcodes = array_merge($commonShortcodes, $templateShortcodes);
                                        $displayShortcodes = array_slice($allShortcodes, 0, 12, true); // Show first 12 shortcodes
                                    @endphp

                                    @foreach($displayShortcodes as $shortcode => $description)
                                        @php
                                            $shortcodeText = '{{' . $shortcode . '}}';
                                            $icon = $shortcodeIcons[$shortcode] ?? 'las la-tag';
                                        @endphp
                                        <button type="button" class="shortcode-btn" data-shortcode="{{ $shortcodeText }}" title="{{ $description }}">
                                            <i class="{{ $icon }}"></i>
                                            {{ $shortcodeText }}
                                        </button>
                                    @endforeach
                                </div>

                                <!-- Editor Mode Tabs -->
                                <div class="editor-mode-tabs">
                                    <div class="editor-tabs">
                                        <button type="button" id="visual-tab" class="editor-tab active">
                                            <i class="las la-eye"></i> @lang('Visual Editor')
                                        </button>
                                        <button type="button" id="html-tab" class="editor-tab">
                                            <i class="las la-code"></i> @lang('HTML Editor')
                                        </button>
                                    </div>
                                    <div class="editor-actions">
                                        <button type="button" id="preview-email" class="btn btn--success btn-sm">
                                            <i class="las la-eye"></i> @lang('Preview')
                                        </button>
                                    </div>
                                </div>

                                <!-- Visual Editor Panel -->
                                <div id="visual-editor-panel" class="editor-panel visual-editor-panel">
                                    <div contenteditable="true" id="visual-editor-content" class="visual-editor-content" placeholder="@lang('Start typing your email content here...')">
                                        {{-- Content loaded by JavaScript from textarea to prevent Windows Server duplication --}}
                                    </div>
                                </div>

                                <!-- HTML Editor Panel -->
                                <div id="html-editor-panel" class="editor-panel html-editor-panel">
                                    <textarea id="html-editor-textarea" class="html-editor-textarea" placeholder="@lang('Enter HTML content here...')">{{ $template->email_body }}</textarea>
                                </div>

                                <!-- Hidden field for final email body content -->
                                <input type="hidden" id="email_body_final" name="email_body_final" value="{{ $template->email_body }}">

                                <!-- Hidden textarea for JavaScript compatibility -->
                                <textarea name="email_body" id="email_body" style="display: none;">{{ $template->email_body }}</textarea>

                                <!-- Base64 encoded content for Windows Server compatibility -->
                                <input type="hidden" name="email_body_encoded" id="email_body_encoded" value="">

                                <input type="hidden" name="template_id" value="{{ $template->id }}">

                            </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SMS Template Section (Full Width) -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg--primary">
                        <h5 class="card-title text-white">
                            <i class="las la-sms"></i> @lang('SMS Template')
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('SMS Status') <span class="text-danger">*</span></label>
                                    <input type="checkbox" data-height="46px" data-width="100%" data-onstyle="-success"
                                       data-offstyle="-danger" data-bs-toggle="toggle" data-on="@lang('Send SMS')"
                                       data-off="@lang("Don't Send")" name="sms_status"
                                       @if($template->sms_status) checked @endif>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>@lang('SMS Message') <span class="text-danger">*</span></label>
                                    <textarea name="sms_body" rows="6" class="form-control" placeholder="@lang('Your SMS message using short-codes')" required>{{ $template->sms_body }}</textarea>
                                    <small class="text-muted">@lang('SMS messages should be concise. Use shortcodes for dynamic content.')</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- BOTTOM SECTION: Form Actions (Full Width) -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="form-actions-section">
                    <button type="submit" class="btn btn--primary w-100 h-45" id="update-template-btn">
                        <i class="las la-save"></i> @lang('Update Template')
                    </button>
                </div>
            </div>
        </div>
    </form>
@endsection


@push('breadcrumb-plugins')
    <x-back route="{{ route('admin.setting.notification.templates') }}" />
@endpush

@push('style')
<!-- Email Editor CSS with Enhanced Windows Server/Plesk Compatibility -->
<link rel="stylesheet" href="{{ asset('assets/admin/css/simple-email-editor.css') }}?v={{ time() }}" id="email-editor-css">

<!-- Critical CSS Inline for Live Server Compatibility -->
<style id="critical-email-editor-css">
/* Critical styles inlined for Windows Server/Plesk compatibility */
.simple-email-editor {
    background: #ffffff !important;
    border-radius: 5px !important;
    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03) !important;
    overflow: hidden !important;
    border: none !important;
}

.shortcode-buttons-bar {
    background: #f3f3f9 !important;
    border-bottom: 1px solid rgba(140, 140, 140, 0.125) !important;
    padding: 20px !important;
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 10px !important;
    align-items: center !important;
}

.shortcode-btn {
    display: inline-flex !important;
    align-items: center !important;
    padding: 8px 15px !important;
    background: #ffffff !important;
    border: 1px solid #E3373F !important;
    color: #E3373F !important;
    border-radius: 5px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-size: 12px !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

.shortcode-btn:hover {
    background: #E3373F !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(227, 55, 63, 0.25) !important;
    text-decoration: none !important;
}

.editor-mode-tabs {
    background: #f3f3f9 !important;
    border-bottom: 1px solid rgba(140, 140, 140, 0.125) !important;
    padding: 0 20px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    min-height: 60px !important;
}

.editor-tab {
    padding: 15px 25px !important;
    background: transparent !important;
    border: none !important;
    color: #5b6e88 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-bottom: 3px solid transparent !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.editor-tab:hover {
    color: #E3373F !important;
    background: rgba(227, 55, 63, 0.05) !important;
}

.editor-tab.active {
    color: #E3373F !important;
    border-bottom-color: #E3373F !important;
    background: rgba(227, 55, 63, 0.05) !important;
    font-weight: 600 !important;
}

.visual-editor-content {
    width: 100% !important;
    min-height: 450px !important;
    border: 1px solid rgba(140, 140, 140, 0.125) !important;
    border-radius: 5px !important;
    padding: 20px !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    background: #ffffff !important;
    resize: vertical !important;
    overflow-y: auto !important;
    word-wrap: break-word !important;
    position: relative !important;
    color: #34495e !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02) !important;
}

.visual-editor-content:focus {
    outline: none !important;
    border-color: #E3373F !important;
    box-shadow: 0 0 0 0.2rem rgba(227, 55, 63, 0.15) !important;
}

.html-editor-textarea {
    width: 100% !important;
    min-height: 450px !important;
    border: 1px solid rgba(140, 140, 140, 0.125) !important;
    border-radius: 5px !important;
    padding: 20px !important;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 13px !important;
    line-height: 1.6 !important;
    background: #f3f3f9 !important;
    color: #34495e !important;
    resize: vertical !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02) !important;
}

.html-editor-textarea:focus {
    outline: none !important;
    border-color: #E3373F !important;
    box-shadow: 0 0 0 0.2rem rgba(227, 55, 63, 0.15) !important;
    background: #ffffff !important;
}

.editor-panel {
    padding: 25px !important;
    min-height: 450px !important;
}

.html-editor-panel {
    display: none !important;
}

.visual-editor-panel {
    display: block !important;
}

.shortcode-buttons-bar .shortcode-label {
    font-weight: 600 !important;
    color: #34495e !important;
    margin-right: 15px !important;
    font-size: 14px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.editor-actions {
    display: flex !important;
    gap: 12px !important;
}

.form-actions {
    background: #ffffff !important;
    border-top: 1px solid rgba(140, 140, 140, 0.125) !important;
    padding: 25px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 20px !important;
}

.sms-editor-section {
    background: #f3f3f9 !important;
    border-top: 1px solid rgba(140, 140, 140, 0.125) !important;
    padding: 25px !important;
}

.sms-editor-textarea {
    width: 100% !important;
    min-height: 140px !important;
    border: 1px solid rgba(140, 140, 140, 0.125) !important;
    border-radius: 5px !important;
    padding: 15px !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    background: #ffffff !important;
    resize: vertical !important;
    color: #34495e !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02) !important;
}

.sms-editor-textarea:focus {
    outline: none !important;
    border-color: #E3373F !important;
    box-shadow: 0 0 0 0.2rem rgba(227, 55, 63, 0.15) !important;
}
</style>

<!-- Windows Server CSS Loading Verification -->
<script>
(function() {
    // Check if CSS loaded properly
    setTimeout(function() {
        const cssLink = document.getElementById('email-editor-css');
        const testElement = document.createElement('div');
        testElement.className = 'simple-email-editor';
        testElement.style.display = 'none';
        document.body.appendChild(testElement);

        const computedStyle = window.getComputedStyle(testElement);
        const hasStyles = computedStyle.backgroundColor === 'rgb(255, 255, 255)' ||
                         computedStyle.borderRadius === '5px' ||
                         computedStyle.boxShadow.includes('rgba(18, 38, 63, 0.03)');

        document.body.removeChild(testElement);

        if (!hasStyles) {
            console.warn('⚠️ Email editor CSS not loaded properly, loading fallback styles');
            loadFallbackCSS();
        } else {
            console.log('✅ Email editor CSS loaded successfully');
        }
    }, 1000);

    function loadFallbackCSS() {
        // Enhanced fallback CSS with MBFX professional styling for Windows Server compatibility
        const style = document.createElement('style');
        style.id = 'email-editor-fallback-css';
        style.textContent = `
            /* Enhanced Email Editor Fallback Styles for Windows Server/Plesk */
            .simple-email-editor {
                background: #ffffff !important;
                border-radius: 5px !important;
                box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03) !important;
                overflow: hidden !important;
                border: none !important;
            }

            .shortcode-buttons-bar {
                background: #f3f3f9 !important;
                border-bottom: 1px solid rgba(140, 140, 140, 0.125) !important;
                padding: 20px !important;
                display: flex !important;
                flex-wrap: wrap !important;
                gap: 10px !important;
                align-items: center !important;
            }

            .shortcode-btn {
                display: inline-flex !important;
                align-items: center !important;
                padding: 8px 15px !important;
                background: #ffffff !important;
                border: 1px solid #E3373F !important;
                color: #E3373F !important;
                border-radius: 5px !important;
                cursor: pointer !important;
                transition: all 0.3s ease !important;
                font-size: 12px !important;
                text-decoration: none !important;
                white-space: nowrap !important;
                font-weight: 500 !important;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
            }

            .shortcode-btn:hover {
                background: #E3373F !important;
                color: #ffffff !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 4px 12px rgba(227, 55, 63, 0.25) !important;
                text-decoration: none !important;
            }

            .shortcode-btn i {
                margin-right: 6px !important;
                font-size: 13px !important;
            }

            .editor-mode-tabs {
                background: #f3f3f9 !important;
                border-bottom: 1px solid rgba(140, 140, 140, 0.125) !important;
                padding: 0 20px !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                min-height: 60px !important;
            }

            .editor-tabs {
                display: flex !important;
                gap: 0 !important;
            }

            .editor-tab {
                padding: 15px 25px !important;
                background: transparent !important;
                border: none !important;
                color: #5b6e88 !important;
                cursor: pointer !important;
                transition: all 0.3s ease !important;
                font-size: 14px !important;
                font-weight: 500 !important;
                border-bottom: 3px solid transparent !important;
                text-transform: uppercase !important;
                letter-spacing: 0.5px !important;
            }

            .editor-tab:hover {
                color: #E3373F !important;
                background: rgba(227, 55, 63, 0.05) !important;
            }

            .editor-tab.active {
                color: #E3373F !important;
                border-bottom-color: #E3373F !important;
                background: rgba(227, 55, 63, 0.05) !important;
                font-weight: 600 !important;
            }

            .editor-panel {
                padding: 25px !important;
                min-height: 450px !important;
                background: #ffffff !important;
            }

            .visual-editor-content {
                width: 100% !important;
                min-height: 450px !important;
                border: 1px solid rgba(140, 140, 140, 0.125) !important;
                border-radius: 5px !important;
                padding: 20px !important;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                font-size: 14px !important;
                line-height: 1.6 !important;
                background: #ffffff !important;
                resize: vertical !important;
                overflow-y: auto !important;
                word-wrap: break-word !important;
                position: relative !important;
                color: #34495e !important;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02) !important;
            }

            .visual-editor-content:focus {
                outline: none !important;
                border-color: #E3373F !important;
                box-shadow: 0 0 0 0.2rem rgba(227, 55, 63, 0.15) !important;
            }

            .html-editor-textarea {
                width: 100% !important;
                min-height: 450px !important;
                border: 1px solid rgba(140, 140, 140, 0.125) !important;
                border-radius: 5px !important;
                padding: 20px !important;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
                font-size: 13px !important;
                line-height: 1.6 !important;
                background: #f3f3f9 !important;
                color: #34495e !important;
                resize: vertical !important;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02) !important;
            }

            .html-editor-textarea:focus {
                outline: none !important;
                border-color: #E3373F !important;
                box-shadow: 0 0 0 0.2rem rgba(227, 55, 63, 0.15) !important;
                background: #ffffff !important;
            }

            .html-editor-panel {
                display: none !important;
                padding: 25px !important;
            }

            #visual-editor-panel {
                display: block;
                padding: 20px;
            }

            .form-actions-section {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin-top: 20px;
            }

            /* Windows Server specific fixes */
            @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
                .editor-tab {
                    border-bottom: 3px solid transparent !important;
                }
                .editor-tab.active {
                    border-bottom: 3px solid #dc3545 !important;
                }
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .shortcode-buttons-bar {
                    padding: 10px;
                    gap: 5px;
                }

                .shortcode-btn {
                    padding: 4px 8px;
                    font-size: 11px;
                }

                .editor-mode-tabs {
                    flex-direction: column;
                    gap: 10px;
                    padding: 10px;
                }

                .visual-editor-content,
                .html-editor-textarea {
                    min-height: 300px;
                    padding: 15px;
                }
            }
        `;

        document.head.appendChild(style);
        console.log('✅ Enhanced fallback CSS loaded for Windows Server/Plesk compatibility');
    }

    // Enhanced verification for Windows Server/Plesk environment
    function verifyAndFixStyles() {
        setTimeout(function() {
            const elements = document.querySelectorAll('.simple-email-editor, .shortcode-btn, .editor-tab, .visual-editor-content, .html-editor-textarea');
            let stylesFixed = 0;

            elements.forEach(el => {
                const computedStyle = window.getComputedStyle(el);

                // Force apply critical styles if not properly loaded
                if (el.classList.contains('simple-email-editor') && computedStyle.borderRadius === '0px') {
                    el.style.cssText += 'background: #ffffff !important; border-radius: 5px !important; box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03) !important;';
                    stylesFixed++;
                }
                if (el.classList.contains('shortcode-btn') && computedStyle.borderColor !== 'rgb(227, 55, 63)') {
                    el.style.cssText += 'border: 1px solid #E3373F !important; color: #E3373F !important; border-radius: 5px !important; background: #ffffff !important;';
                    stylesFixed++;
                }
                if (el.classList.contains('editor-tab') && computedStyle.color !== 'rgb(91, 110, 136)') {
                    el.style.cssText += 'color: #5b6e88 !important; border-bottom: 3px solid transparent !important; text-transform: uppercase !important;';
                    stylesFixed++;
                }
                if ((el.classList.contains('visual-editor-content') || el.classList.contains('html-editor-textarea')) && computedStyle.borderRadius === '0px') {
                    el.style.cssText += 'border: 1px solid rgba(140, 140, 140, 0.125) !important; border-radius: 5px !important; padding: 20px !important;';
                    stylesFixed++;
                }
            });

            if (stylesFixed > 0) {
                console.log(`🔧 Fixed ${stylesFixed} style elements for Windows Server compatibility`);
            }
        }, 2000);
    }

    // Run verification multiple times for reliability
    verifyAndFixStyles();
    setTimeout(verifyAndFixStyles, 5000);
})();
</script>
@endpush


@push('script')
<!-- Email Editor External JavaScript -->
<script src="{{ asset('assets/admin/js/simple-email-editor.js') }}?v={{ time() }}"></script>

<!-- Template Data Configuration -->
<script>
// Set template data for external JavaScript
if (typeof setTemplateData === 'function') {
    setTemplateData({
        content: {!! json_encode($template->email_body) !!},
        templateId: {{ $template->id }},
        testEmailRoute: '{{ route("admin.setting.notification.template.test") }}',
        previewRoute: '{{ route("admin.setting.notification.template.preview", ":id") }}',
        csrfToken: '{{ csrf_token() }}',
        debug: {
            templateName: '{{ $template->name ?? "Unknown" }}',
            templateSubject: '{{ $template->subject ?? "Unknown" }}',
            contentLength: {{ strlen($template->email_body ?? '') }},
            serverTime: '{{ now()->toISOString() }}',
            laravelVersion: '{{ app()->version() }}',
            phpVersion: '{{ PHP_VERSION }}'
        }
    });
}
</script>

<!-- Email Editor Initialization -->
<script>
// Initialize email editor when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Set configuration
    if (typeof window.EmailEditorConfig !== 'undefined') {
        window.EmailEditorConfig.debug = window.location.hostname === 'localhost' || window.location.search.includes('debug=1');
        window.EmailEditorConfig.templateId = {{ $template->id ?? 'null' }};
    }

    // Initialize email editor using external JavaScript
    if (typeof initializeSimpleEditor === 'function') {
        initializeSimpleEditor();
    } else {
        console.error('Email editor external JavaScript not loaded');
    }

});

    // Handle jQuery loading failure with multiple fallback strategies
    function handleJQueryFailure(callback) {
        log('🔄 Attempting jQuery recovery strategies...');

        // Strategy 1: Check if jQuery is loaded but not assigned to global variables
        const scripts = document.querySelectorAll('script[src*="jquery"]');
        if (scripts.length > 0) {
            log('📄 Found jQuery script tags, attempting to re-initialize...');
            setTimeout(() => {
                if (typeof $ !== 'undefined' || typeof jQuery !== 'undefined') {
                    log('✅ jQuery found after re-check');
                    callback();
                    return;
                }
                loadJQueryFromCDN(callback);
            }, 1000);
        } else {
            loadJQueryFromCDN(callback);
        }
    }

    // Load jQuery from CDN as last resort
    function loadJQueryFromCDN(callback) {
        log('🌐 Loading jQuery from CDN as fallback...');

        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js';
        script.crossOrigin = 'anonymous';
        script.integrity = 'sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==';

        script.onload = function() {
            log('✅ jQuery loaded from CDN');
            window.$ = window.jQuery = jQuery.noConflict(true);
            callback();
        };

        script.onerror = function() {
            log('❌ Failed to load jQuery from CDN', 'error');
            showCriticalError('jQuery could not be loaded from any source. Please refresh the page.');
        };

        document.head.appendChild(script);
    }

    // Show critical error with Windows Server styling
    function showCriticalError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
            border: 2px solid #dc3545;
            background: #f8d7da;
            color: #721c24;
            font-weight: 500;
        `;
        errorDiv.innerHTML = `
            <button type="button" class="btn-close" onclick="this.parentElement.remove()" style="float: right;"></button>
            <strong>⚠️ Email Editor Error:</strong><br>${message}
            <br><small>Check browser console for details.</small>
        `;
        document.body.appendChild(errorDiv);
    }

    // Main initialization function
    function initializeEmailEditor() {
        log('🔧 Initializing Email Template Editor...');

        try {
            // Verify jQuery is truly available
            if (typeof $ === 'undefined' || !$.fn) {
                throw new Error('jQuery is not properly initialized');
            }

            // Verify required DOM elements exist
            const requiredElements = {
                visualEditor: '#visual-editor-content',
                htmlEditor: '#html-editor-textarea', // Updated selector
                emailBodyField: 'textarea[name="email_body"]',
                visualTab: '#visual-tab',
                htmlTab: '#html-tab'
            };

            const missingElements = [];
            for (const [name, selector] of Object.entries(requiredElements)) {
                if ($(selector).length === 0) {
                    missingElements.push(`${name} (${selector})`);
                }
            }

            if (missingElements.length > 0) {
                throw new Error('Missing required elements: ' + missingElements.join(', '));
            }

            log('✅ All required elements found');

            // Initialize components
            initializeEditorContent();
            setupEventHandlers();
            initializeShortcodes();
            setupAutoSave();

            // Mark as initialized
            window.emailEditorInitialized = true;

            log('✅ Email Template Editor initialized successfully');
            showSuccessNotification('Email editor loaded successfully');

        } catch (error) {
            log('❌ Error initializing editor: ' + error.message, 'error');
            console.error('Email Editor Initialization Error:', error);
            showCriticalError('Failed to initialize editor: ' + error.message);
        }
    }

    // Show success notification
    function showSuccessNotification(message) {
        if (!EMAIL_EDITOR_CONFIG.debug) return;

        const successDiv = document.createElement('div');
        successDiv.className = 'alert alert-success';
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 300px;
            opacity: 0.9;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        `;
        successDiv.innerHTML = `<strong>✅ ${message}</strong>`;
        document.body.appendChild(successDiv);

        setTimeout(() => {
            successDiv.style.transition = 'opacity 0.5s';
            successDiv.style.opacity = '0';
            setTimeout(() => successDiv.remove(), 500);
        }, 3000);
    }

    // Initialize editor content with Windows Server compatibility
    function initializeEditorContent() {
        log('📝 Initializing editor content...');

        try {
            const $emailBodyField = $('textarea[name="email_body"]');
            const $visualEditor = $('#visual-editor-content');
            const $htmlEditor = $('#html-editor-textarea');

            let content = $emailBodyField.val() || '';

            // Clean content for Windows Server
            content = cleanHtmlContentForWindows(content);

            // Set content in both editors
            $visualEditor.html(content);
            $htmlEditor.val(content);

            log('✅ Editor content initialized (' + content.length + ' chars)');

        } catch (error) {
            log('❌ Error initializing content: ' + error.message, 'error');
        }
    }

    // Enhanced HTML cleaning for Windows Server
    function cleanHtmlContentForWindows(html) {
        if (!html) return '';

        // Fix Windows line endings
        html = html.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

        // Fix Windows encoding issues
        html = html.replace(/â€™/g, "'")
                  .replace(/â€œ/g, '"')
                  .replace(/â€/g, '"')
                  .replace(/â€¦/g, '...')
                  .replace(/â€"/g, '—');

        // Clean excessive whitespace
        html = html.replace(/\n\s*\n\s*\n/g, '\n\n');

        return html.trim();
    }

    // Set up event handlers with jQuery
    function setupEventHandlers() {
        log('🔗 Setting up event handlers...');

        try {
            // Remove any existing handlers first
            $('#visual-tab, #html-tab').off('click.emailEditor');
            $('#html-editor-textarea').off('input.emailEditor');
            $('form').off('submit.emailEditor');

            // Editor mode switching
            $('#visual-tab').on('click.emailEditor', function(e) {
                e.preventDefault();
                switchEditorMode('visual');
            });

            $('#html-tab').on('click.emailEditor', function(e) {
                e.preventDefault();
                switchEditorMode('html');
            });

            // Content synchronization
            $('#html-editor-textarea').on('input.emailEditor', function() {
                syncEditorContent();
            });

            // Form submission
            $('form').on('submit.emailEditor', function() {
                updateHiddenFields();
            });

            log('✅ Event handlers set up');

        } catch (error) {
            log('❌ Error setting up handlers: ' + error.message, 'error');
        }
    }

    // Switch editor mode function (global for external access)
    window.switchEditorMode = function(mode) {
        log(`🔄 Switching to ${mode} mode`);

        try {
            if (mode === 'visual') {
                $('#visual-editor-panel').show();
                $('#html-editor-panel').hide();
                $('#visual-tab').addClass('active');
                $('#html-tab').removeClass('active');
                syncContentToVisual();
            } else {
                $('#visual-editor-panel').hide();
                $('#html-editor-panel').show();
                $('#visual-tab').removeClass('active');
                $('#html-tab').addClass('active');
                syncContentToHtml();
            }

            log('✅ Switched to ' + mode + ' mode');

        } catch (error) {
            log('❌ Error switching mode: ' + error.message, 'error');
        }
    };

    // Sync functions
    function syncEditorContent() {
        try {
            const htmlContent = $('#html-editor-textarea').val();
            const cleanContent = cleanHtmlContentForWindows(htmlContent);
            $('#visual-editor-content').html(cleanContent);
        } catch (error) {
            log('❌ Error syncing content: ' + error.message, 'error');
        }
    }

    function syncContentToVisual() {
        try {
            const htmlContent = $('#html-editor-textarea').val();
            const cleanContent = cleanHtmlContentForWindows(htmlContent);
            $('#visual-editor-content').html(cleanContent);
        } catch (error) {
            log('❌ Error syncing to visual: ' + error.message, 'error');
        }
    }

    function syncContentToHtml() {
        try {
            const visualContent = $('#visual-editor-content').html();
            $('#html-editor-textarea').val(visualContent);
        } catch (error) {
            log('❌ Error syncing to HTML: ' + error.message, 'error');
        }
    }

    // Update hidden fields
    function updateHiddenFields() {
        log('💾 Updating hidden fields...');

        try {
            const htmlContent = $('#html-editor-textarea').val();
            const cleanContent = cleanHtmlContentForWindows(htmlContent);
            $('textarea[name="email_body"]').val(cleanContent);

            log('✅ Hidden fields updated (' + cleanContent.length + ' chars)');

        } catch (error) {
            log('❌ Error updating fields: ' + error.message, 'error');
        }
    }

    // Initialize shortcodes
    function initializeShortcodes() {
        log('🏷️ Initializing shortcodes...');

        try {
            $('.shortcode-btn').off('click.emailEditor').on('click.emailEditor', function() {
                const shortcode = $(this).data('shortcode');
                if (shortcode) {
                    insertShortcode(shortcode);
                }
            });

            log('✅ Shortcodes initialized');

        } catch (error) {
            log('❌ Error initializing shortcodes: ' + error.message, 'error');
        }
    }

    // Insert shortcode
    function insertShortcode(shortcode) {
        try {
            const htmlEditor = $('#html-editor-textarea')[0];
            if (htmlEditor) {
                const cursorPos = htmlEditor.selectionStart;
                const textBefore = htmlEditor.value.substring(0, cursorPos);
                const textAfter = htmlEditor.value.substring(cursorPos);

                htmlEditor.value = textBefore + shortcode + textAfter;
                htmlEditor.focus();
                htmlEditor.setSelectionRange(cursorPos + shortcode.length, cursorPos + shortcode.length);

                syncEditorContent();
                log('✅ Shortcode inserted: ' + shortcode);
            }
        } catch (error) {
            log('❌ Error inserting shortcode: ' + error.message, 'error');
        }
    }

    // Set up auto-save
    function setupAutoSave() {
        log('💾 Setting up auto-save...');

        try {
            let autoSaveTimer;

            $('#html-editor-textarea').on('input.autosave', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(() => {
                    updateHiddenFields();
                    log('💾 Auto-saved');
                }, 2000);
            });

            log('✅ Auto-save enabled');

        } catch (error) {
            log('❌ Error setting up auto-save: ' + error.message, 'error');
        }
    }

    // Start initialization
    log('🔍 Ensuring jQuery availability...');
    ensureJQuery(initializeEmailEditor);

})();
</script>

<!-- Simple Direct Email Editor Loading -->
<script src="{{ asset('assets/admin/js/simple-email-editor.js') }}"></script>

<script>
// Simple initialization after DOM is ready
$(document).ready(function() {
    console.log('🔧 [EMAIL-EDITOR] Starting simple initialization...');

    // Wait for dependencies
    if (typeof $ === 'undefined') {
        console.error('❌ [EMAIL-EDITOR] jQuery not available');
        return;
    }

    if (typeof bkLib === 'undefined') {
        console.warn('⚠️ [EMAIL-EDITOR] nicEdit not available, continuing without it');
    }

    // Initialize test email functionality
    const testEmailBtn = $('#send-test-email');
    if (testEmailBtn.length > 0) {
        testEmailBtn.off('click.emailEditor').on('click.emailEditor', function(e) {
            e.preventDefault();
            console.log('📧 [EMAIL-EDITOR] Test email button clicked');

            const testEmail = $('#test-email-address').val();
            if (!testEmail) {
                notify('error', 'Please enter an email address');
                return;
            }

            // Show loading
            testEmailBtn.prop('disabled', true).html('<i class="las la-spinner la-spin"></i> Sending...');

            // Prepare data
            const formData = new FormData();
            formData.append('test_email', testEmail);
            formData.append('template_id', {{ $template->id }});
            formData.append('_token', '{{ csrf_token() }}');
            formData.append('email_body', $('#html-editor-textarea').val() || '');

            // Send request
            fetch('{{ route("admin.setting.notification.template.test") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                testEmailBtn.prop('disabled', false).html('<i class="las la-paper-plane"></i> Send');

                if (data.status === 'success') {
                    $('#test-email-result').show().html('<div class="alert alert-success">' + data.message + '</div>');
                    notify('success', data.message);
                } else {
                    $('#test-email-result').show().html('<div class="alert alert-danger">' + (data.message || 'Failed to send test email') + '</div>');
                    notify('error', data.message || 'Failed to send test email');
                }
            })
            .catch(error => {
                console.error('Test email error:', error);
                testEmailBtn.prop('disabled', false).html('<i class="las la-paper-plane"></i> Send');
                $('#test-email-result').show().html('<div class="alert alert-danger">Error sending test email</div>');
                notify('error', 'Error sending test email');
            });
        });

        console.log('✅ [EMAIL-EDITOR] Test email functionality initialized');
    }

    // Initialize editor tabs with proper content synchronization
    $('#visual-tab').on('click', function(e) {
        e.preventDefault();
        console.log('🔄 [EMAIL-EDITOR] Switching to Visual mode');

        // Sync content from HTML to Visual before switching
        const htmlContent = $('#html-editor-textarea').val() || '';
        $('#visual-editor-content').html(htmlContent);

        // Update UI
        $('#visual-editor-panel').show();
        $('#html-editor-panel').hide();
        $(this).addClass('active');
        $('#html-tab').removeClass('active');

        console.log('✅ [EMAIL-EDITOR] Switched to Visual mode, content synced');
    });

    $('#html-tab').on('click', function(e) {
        e.preventDefault();
        console.log('🔄 [EMAIL-EDITOR] Switching to HTML mode');

        // Sync content from Visual to HTML before switching
        const visualContent = $('#visual-editor-content').html() || '';
        $('#html-editor-textarea').val(visualContent);

        // Update UI
        $('#html-editor-panel').show();
        $('#visual-editor-panel').hide();
        $(this).addClass('active');
        $('#visual-tab').removeClass('active');

        console.log('✅ [EMAIL-EDITOR] Switched to HTML mode, content synced');
    });

    // Add content change listeners for real-time sync
    $('#visual-editor-content').on('input blur', function() {
        const content = $(this).html();
        $('#html-editor-textarea').val(content);
        console.log('🔄 [EMAIL-EDITOR] Visual content synced to HTML');
    });

    $('#html-editor-textarea').on('input blur', function() {
        const content = $(this).val();
        $('#visual-editor-content').html(content);
        console.log('🔄 [EMAIL-EDITOR] HTML content synced to Visual');
    });

    // Replace form submission with AJAX for Windows Server compatibility
    $('form').on('submit', function(e) {
        e.preventDefault();
        console.log('🔄 [EMAIL-EDITOR] Starting AJAX save...');

        saveTemplateViaAjax();
        return false;
    });

    // Add save button click handler
    $('.btn[type="submit"]').on('click', function(e) {
        e.preventDefault();
        saveTemplateViaAjax();
        return false;
    });

    console.log('✅ [EMAIL-EDITOR] Simple initialization completed');
});

// AJAX Template Save Function with Base64 Encoding for Windows Server
function saveTemplateViaAjax() {
    console.log('💾 [EMAIL-EDITOR] Starting AJAX save with base64 encoding...');

    try {
        // Show loading state
        const saveBtn = $('.btn[type="submit"]');
        const originalText = saveBtn.html();
        saveBtn.prop('disabled', true).html('<i class="las la-spinner la-spin"></i> Saving...');

        // Prepare form data
        const formData = new FormData();
        formData.append('_token', '{{ csrf_token() }}');
        formData.append('template_id', {{ $template->id }});

        // Get form values
        formData.append('subject', $('input[name="subject"]').val() || '');
        formData.append('email_status', $('input[name="email_status"]').is(':checked') ? 1 : 0);
        formData.append('sms_status', $('input[name="sms_status"]').is(':checked') ? 1 : 0);
        formData.append('sms_body', $('textarea[name="sms_body"]').val() || '');

        // Get HTML content and encode it for Windows Server compatibility
        const htmlContent = $('#html-editor-textarea').val() || '';
        console.log('📝 [EMAIL-EDITOR] Original content length:', htmlContent.length);

        // Base64 encode the content to prevent corruption on Windows Server
        let encodedContent = '';
        try {
            encodedContent = btoa(unescape(encodeURIComponent(htmlContent)));
            console.log('🔐 [EMAIL-EDITOR] Content encoded successfully, length:', encodedContent.length);
        } catch (encodeError) {
            console.error('❌ [EMAIL-EDITOR] Base64 encoding failed:', encodeError);
            // Fallback to regular content
            formData.append('email_body', htmlContent);
        }

        if (encodedContent) {
            formData.append('email_body_encoded', encodedContent);
        }

        // Also send regular content as fallback
        formData.append('email_body', htmlContent);
        formData.append('email_body_final', htmlContent);

        // Update hidden fields for consistency
        $('textarea[name="email_body"]').val(htmlContent);
        $('#email_body_final').val(htmlContent);
        $('#email_body_encoded').val(encodedContent);

        console.log('📤 [EMAIL-EDITOR] Sending AJAX request...');

        // Send AJAX request
        fetch('{{ route("admin.setting.notification.template.update", $template->id) }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('📥 [EMAIL-EDITOR] Response received, status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📋 [EMAIL-EDITOR] Response data:', data);

            // Restore button state
            saveBtn.prop('disabled', false).html(originalText);

            if (data.status === 'success') {
                console.log('✅ [EMAIL-EDITOR] Template saved successfully');

                // Show success notification using Laravel notify only
                notify('success', data.message || 'Template saved successfully');

                // Update template data if provided
                if (data.template) {
                    window.templateData.content = data.template.email_body;
                    console.log('🔄 [EMAIL-EDITOR] Template data updated');
                }

            } else {
                console.error('❌ [EMAIL-EDITOR] Save failed:', data.message);

                // Show error notification using Laravel notify only
                notify('error', data.message || 'Failed to save template');
            }
        })
        .catch(error => {
            console.error('❌ [EMAIL-EDITOR] AJAX save error:', error);

            // Restore button state
            saveBtn.prop('disabled', false).html(originalText);

            // Show error notification using Laravel notify only
            notify('error', 'Error saving template. Please try again.');
        });

    } catch (error) {
        console.error('❌ [EMAIL-EDITOR] Save function error:', error);

        // Restore button state
        $('.btn[type="submit"]').prop('disabled', false);

        notify('error', 'Error preparing template data');
    }
}
</script>

<!-- Fallback Test Email Handler -->
<script>
// Ensure test email functionality works even if main script has issues
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for other scripts to load
    setTimeout(function() {
        const sendTestEmailBtn = document.getElementById('send-test-email');

        if (sendTestEmailBtn && !sendTestEmailBtn.hasAttribute('data-handler-attached')) {
            sendTestEmailBtn.setAttribute('data-handler-attached', 'true');

            sendTestEmailBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('📧 Fallback test email handler triggered');

                // Try to use the main function if available
                if (typeof sendTestEmail === 'function') {
                    sendTestEmail();
                } else {
                    // Fallback implementation
                    handleTestEmailFallback();
                }
            });

            console.log('✅ Fallback test email handler attached');
        }
    }, 1000);
});

// Fallback test email implementation
function handleTestEmailFallback() {
    const testEmailInput = document.getElementById('test-email-address');
    const testEmail = testEmailInput?.value;
    const resultDiv = document.getElementById('test-email-result');

    if (!testEmail || !testEmail.trim()) {
        notify('error', 'Please enter a valid email address');
        return;
    }

    // Show loading state
    const sendBtn = document.getElementById('send-test-email');
    if (sendBtn) {
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="las la-spinner la-spin"></i> Sending...';
    }

    if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '<div class="alert alert-info"><i class="las la-spinner la-spin"></i> Sending test email...</div>';
    }

    // Prepare form data
    const formData = new FormData();
    formData.append('test_email', testEmail);
    formData.append('template_id', window.templateData?.templateId || {{ $template->id }});
    formData.append('_token', window.templateData?.csrfToken || '{{ csrf_token() }}');

    // Get current email content
    const emailBody = document.getElementById('html-editor-textarea')?.value || '';
    formData.append('email_body', emailBody);

    // Send request
    const testEmailRoute = window.templateData?.testEmailRoute || '{{ route("admin.setting.notification.template.test") }}';

    fetch(testEmailRoute, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        // Reset button
        if (sendBtn) {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="las la-paper-plane"></i> @lang("Send")';
        }

        if (data.status === 'success') {
            if (resultDiv) {
                resultDiv.innerHTML = '<div class="alert alert-success"><i class="las la-check"></i> ' + data.message + '</div>';
            }
            notify('success', data.message);
        } else {
            if (resultDiv) {
                resultDiv.innerHTML = '<div class="alert alert-danger"><i class="las la-times"></i> ' + (data.message || 'Failed to send test email') + '</div>';
            }
            notify('error', data.message || 'Failed to send test email');
        }
    })
    .catch(error => {
        console.error('Test email error:', error);

        // Reset button
        if (sendBtn) {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="las la-paper-plane"></i> @lang("Send")';
        }

        if (resultDiv) {
            resultDiv.innerHTML = '<div class="alert alert-danger"><i class="las la-times"></i> Error sending test email</div>';
        }

        notify('error', 'Error sending test email');
    });
}
</script>
@endpush