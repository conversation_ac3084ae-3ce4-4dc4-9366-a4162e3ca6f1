/**
 * Professional Email Template Editor Styles
 * Designed for MBFX Admin Theme
 * Uses MBFX color scheme: Primary #E3373F, Success #28c76f, Dark #34495e
 */

/* Main Editor Container */
.simple-email-editor {
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);
    overflow: hidden;
    border: none;
}

/* Shortcode Buttons Bar */
.shortcode-buttons-bar {
    background: #f3f3f9;
    border-bottom: 1px solid rgba(140, 140, 140, 0.125);
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.shortcode-buttons-bar .shortcode-label {
    font-weight: 600;
    color: #34495e;
    margin-right: 15px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.shortcode-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 15px;
    background: #ffffff;
    border: 1px solid #E3373F;
    color: #E3373F;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    text-decoration: none;
    white-space: nowrap;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.shortcode-btn:hover {
    background: #E3373F;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(227, 55, 63, 0.25);
    text-decoration: none;
}

.shortcode-btn i {
    margin-right: 6px;
    font-size: 13px;
}

/* Editor Mode Tabs */
.editor-mode-tabs {
    background: #f3f3f9;
    border-bottom: 1px solid rgba(140, 140, 140, 0.125);
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60px;
}

.editor-tabs {
    display: flex;
    gap: 0;
}

.editor-tab {
    padding: 15px 25px;
    background: transparent;
    border: none;
    color: #5b6e88;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.editor-tab:hover {
    color: #E3373F;
    background: rgba(227, 55, 63, 0.05);
}

.editor-tab.active {
    color: #E3373F;
    border-bottom-color: #E3373F;
    background: rgba(227, 55, 63, 0.05);
    font-weight: 600;
}

.editor-actions {
    display: flex;
    gap: 12px;
}

/* Editor Panels */
.editor-panel {
    padding: 25px;
    min-height: 450px;
}

.visual-editor-panel {
    display: block;
}

.html-editor-panel {
    display: none;
}

/* Visual Editor */
.visual-editor-content {
    width: 100%;
    min-height: 450px;
    border: 1px solid rgba(140, 140, 140, 0.125);
    border-radius: 5px;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    background: #ffffff;
    resize: vertical;
    overflow-y: auto;
    word-wrap: break-word;
    position: relative;
    color: #34495e;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.visual-editor-content:focus {
    outline: none;
    border-color: #E3373F;
    box-shadow: 0 0 0 0.2rem rgba(227, 55, 63, 0.15);
}

/* Enhanced Email Template Styling within Visual Editor */
.visual-editor-content table {
    border-collapse: collapse;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.visual-editor-content table td {
    padding: 12px;
    vertical-align: top;
    border: 0;
}

.visual-editor-content img {
    max-width: 100%;
    height: auto;
    display: block;
    border: 0;
    border-radius: 4px;
}

.visual-editor-content h1,
.visual-editor-content h2,
.visual-editor-content h3 {
    margin: 15px 0 10px 0;
    color: #34495e;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
}



.visual-editor-content a {
    color: #E3373F;
    text-decoration: none;
    font-weight: 500;
}

.visual-editor-content a:hover {
    text-decoration: underline;
    color: #01A368;
}

/* Email template container styling */
.visual-editor-content [style*="background"] {
    border-radius: 5px;
}

/* Email template specific styling */
.visual-editor-content .email-header {
    background: #E3373F;
    color: white;
    padding: 25px;
    text-align: center;
    border-radius: 5px 5px 0 0;
}

.visual-editor-content .email-body {
    padding: 25px;
    background: #ffffff;
}

.visual-editor-content .email-footer {
    background: #f3f3f9;
    padding: 20px;
    text-align: center;
    font-size: 12px;
    color: #5b6e88;
    border-radius: 0 0 5px 5px;
}

.visual-editor-content .btn {
    display: inline-block;
    padding: 12px 25px;
    background: #E3373F;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(227, 55, 63, 0.2);
}

.visual-editor-content .btn:hover {
    background: #01A368;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(1, 163, 104, 0.3);
}

/* Placeholder styling */
.visual-editor-content:empty:before {
    content: attr(placeholder);
    color: #5b6e88;
    font-style: italic;
    pointer-events: none;
}

/* Email template preview mode */
.visual-editor-content.preview-mode {
    background: #f3f3f9;
    border: 2px dashed rgba(140, 140, 140, 0.125);
}

.visual-editor-content.preview-mode::before {
    content: "📧 Email Template Preview";
    display: block;
    text-align: center;
    padding: 12px;
    background: #E3373F;
    color: white;
    margin: -20px -20px 20px -20px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* HTML Editor */
.html-editor-textarea {
    width: 100%;
    min-height: 450px;
    border: 1px solid rgba(140, 140, 140, 0.125);
    border-radius: 5px;
    padding: 20px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    background: #f3f3f9;
    color: #34495e;
    resize: vertical;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.html-editor-textarea:focus {
    outline: none;
    border-color: #E3373F;
    box-shadow: 0 0 0 0.2rem rgba(227, 55, 63, 0.15);
    background: #ffffff;
}

/* SMS Editor Section */
.sms-editor-section {
    background: #f3f3f9;
    border-top: 1px solid rgba(140, 140, 140, 0.125);
    padding: 25px;
}

.sms-editor-section .form-label {
    font-weight: 600;
    color: #34495e;
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 13px;
}

.sms-editor-textarea {
    width: 100%;
    min-height: 140px;
    border: 1px solid rgba(140, 140, 140, 0.125);
    border-radius: 5px;
    padding: 15px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    background: #ffffff;
    resize: vertical;
    color: #34495e;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.sms-editor-textarea:focus {
    outline: none;
    border-color: #E3373F;
    box-shadow: 0 0 0 0.2rem rgba(227, 55, 63, 0.15);
}

/* Form Actions */
.form-actions {
    background: #ffffff;
    border-top: 1px solid rgba(140, 140, 140, 0.125);
    padding: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.form-actions .btn {
    min-width: 140px;
    font-weight: 500;
}

/* Test Email Section */
.test-email-section {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.test-email-input {
    min-width: 250px;
    flex: 1;
}

.test-email-result {
    margin-top: 15px;
    width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .shortcode-buttons-bar {
        padding: 15px;
    }

    .shortcode-btn {
        font-size: 11px;
        padding: 6px 10px;
    }

    .editor-mode-tabs {
        flex-direction: column;
        align-items: stretch;
        padding: 15px;
        gap: 15px;
        min-height: auto;
    }

    .editor-tabs {
        justify-content: center;
    }

    .editor-actions {
        justify-content: center;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
        padding: 20px;
    }

    .test-email-section {
        flex-direction: column;
        align-items: stretch;
    }

    .editor-panel {
        padding: 15px;
    }

    .visual-editor-content,
    .html-editor-textarea {
        min-height: 350px;
    }
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 18px;
    height: 18px;
    top: 50%;
    left: 50%;
    margin-left: -9px;
    margin-top: -9px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    top: 25px;
    right: 25px;
    z-index: 9999;
    min-width: 320px;
    max-width: 520px;
    padding: 18px 20px;
    border-radius: 5px;
    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.15);
    font-size: 14px;
    font-weight: 500;
    border-left: 4px solid;
}

/* Success/Error States */
.alert-success {
    background-color: rgba(40, 199, 111, 0.1);
    border-color: #28c76f;
    border-left-color: #28c76f;
    color: #28c76f;
}

.alert-danger {
    background-color: rgba(234, 84, 85, 0.1);
    border-color: #ea5455;
    border-left-color: #ea5455;
    color: #ea5455;
}

.alert-info {
    background-color: rgba(30, 159, 242, 0.1);
    border-color: #1e9ff2;
    border-left-color: #1e9ff2;
    color: #1e9ff2;
}

/* Preview Modal Styles */
.preview-controls {
    background: #f3f3f9;
    border-bottom: 1px solid rgba(140, 140, 140, 0.125);
    padding: 15px 20px;
}

.preview-mode-buttons {
    display: flex;
    gap: 12px;
}

.preview-mode-buttons .btn {
    border-radius: 5px;
    font-size: 12px;
    padding: 8px 16px;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.preview-mode-buttons .btn.active {
    background-color: #E3373F !important;
    border-color: #E3373F !important;
    color: #fff !important;
    box-shadow: 0 2px 4px rgba(227, 55, 63, 0.3);
}

.preview-container {
    background: #f3f3f9;
    min-height: 650px;
}

.preview-frame {
    padding: 25px;
}

.mobile-frame {
    background: #34495e;
    padding: 15px;
    border-radius: 25px;
    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.2);
}

.mobile-frame iframe {
    border-radius: 15px;
    border: none;
}

/* Modal Enhancements */
.modal-xl {
    max-width: 1300px;
}

.modal-header.bg--primary {
    background-color: #E3373F !important;
    border-bottom: 1px solid #E3373F;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #fff;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.btn-close:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* Professional Enhancements */
.simple-email-editor .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(140, 140, 140, 0.125);
    padding: 20px 25px;
}

.simple-email-editor .card-title {
    color: #34495e;
    font-weight: 600;
    margin: 0;
    font-size: 16px;
}

/* Focus States */
.simple-email-editor input:focus,
.simple-email-editor select:focus,
.simple-email-editor textarea:focus {
    border-color: #E3373F;
    box-shadow: 0 0 0 0.2rem rgba(227, 55, 63, 0.15);
    outline: none;
}

/* Button Enhancements */
.simple-email-editor .btn--success {
    background-color: #28c76f;
    border-color: #28c76f;
    color: #ffffff;
    font-weight: 500;
}

.simple-email-editor .btn--success:hover {
    background-color: #24b263;
    border-color: #24b263;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 199, 111, 0.3);
}

.simple-email-editor .btn--primary {
    background-color: #E3373F;
    border-color: #E3373F;
    color: #ffffff;
    font-weight: 500;
}

.simple-email-editor .btn--primary:hover {
    background-color: #01A368;
    border-color: #01A368;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(1, 163, 104, 0.3);
}

/* Input Group Styling */
.simple-email-editor .input-group-text {
    background-color: #f3f3f9;
    border-color: rgba(140, 140, 140, 0.125);
    color: #5b6e88;
    font-weight: 500;
}

/* Form Labels */
.simple-email-editor .form-label {
    color: #34495e;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
}

/* Scrollbar Styling */
.visual-editor-content::-webkit-scrollbar,
.html-editor-textarea::-webkit-scrollbar {
    width: 6px;
}

.visual-editor-content::-webkit-scrollbar-track,
.html-editor-textarea::-webkit-scrollbar-track {
    background: #f3f3f9;
    border-radius: 3px;
}

.visual-editor-content::-webkit-scrollbar-thumb,
.html-editor-textarea::-webkit-scrollbar-thumb {
    background: #E3373F;
    border-radius: 3px;
}

.visual-editor-content::-webkit-scrollbar-thumb:hover,
.html-editor-textarea::-webkit-scrollbar-thumb:hover {
    background: #01A368;
}
