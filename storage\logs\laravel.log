[2025-06-30 15:54:53] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 15:54:54] production.INFO: Template ID: 44  
[2025-06-30 15:54:54] production.INFO: Request Method: POST  
[2025-06-30 15:54:54] production.INFO: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/44  
[2025-06-30 15:54:54] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 15:54:54] production.INFO: All Request Data: {"_token":"wXhpYNXjoV1G8hmyoOBJ5RaeqtV8Jn5rLg80GkJd","subject":"Account Verification Required - Action Needed","email_status":"on","email_body_final":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n<script type=\"text\/javascript\" class=\"flasher-js\">(function() {    var rootScript = 'https:\/\/cdn.jsdelivr.net\/npm\/@flasher\/flasher@1.3.2\/dist\/flasher.min.js';    var FLASHER_FLASH_BAG_PLACE_HOLDER = {};    var options = mergeOptions([], FLASHER_FLASH_BAG_PLACE_HOLDER);    function mergeOptions(first, second) {        return {            context: merge(first.context || {}, second.context || {}),            envelopes: merge(first.envelopes || [], second.envelopes || []),            options: merge(first.options || {}, second.options || {}),            scripts: merge(first.scripts || [], second.scripts || []),            styles: merge(first.styles || [], second.styles || []),        };    }    function merge(first, second) {        if (Array.isArray(first) && Array.isArray(second)) {            return first.concat(second).filter(function(item, index, array) {                return array.indexOf(item) === index;            });        }        return Object.assign({}, first, second);    }    function renderOptions(options) {        if(!window.hasOwnProperty('flasher')) {            console.error('Flasher is not loaded');            return;        }        requestAnimationFrame(function () {            window.flasher.render(options);        });    }    function render(options) {        if ('loading' !== document.readyState) {            renderOptions(options);            return;        }        document.addEventListener('DOMContentLoaded', function() {            renderOptions(options);        });    }    if (1 === document.querySelectorAll('script.flasher-js').length) {        document.addEventListener('flasher:render', function (event) {            render(event.detail);        });            }    if (window.hasOwnProperty('flasher') || !rootScript || document.querySelector('script[src=\"' + rootScript + '\"]')) {        render(options);    } else {        var tag = document.createElement('script');        tag.setAttribute('src', rootScript);        tag.setAttribute('type', 'text\/javascript');        tag.onload = function () {            render(options);        };        document.head.appendChild(tag);    }})();<\/script>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f4f4f4;\">\r\n        <tbody><tr>\r\n            <td align=\"center\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;\">\r\n                            <h1 style=\"margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: 60px; width: auto; display: block; margin: 0 auto;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 30px 40px 20px; text-align: center;\">\r\n                            <h2 style=\"margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;\">Account Verification Requireded<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\">\r\n                            <p style=\"margin: 0; color: #6c757d; font-size: 16px;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 20px 40px; color: #333333;\">\r\n                            <p>Dear {{fullname}},<\/p><p>Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;\">\r\n                            <p style=\"margin: 0 0 10px 0; font-size: 16px;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: #6c757d; margin: 15px 0 0 0;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;\">\r\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: #ffffff; text-decoration: none;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: #ffffff; text-decoration: none;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: #ffffff; text-decoration: none;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 15px 0 0 0; font-size: 14px;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: #999999; margin: 10px 0 0 0;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: #999999; text-decoration: none;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>","email_body":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n<script type=\"text\/javascript\" class=\"flasher-js\">(function() {    var rootScript = 'https:\/\/cdn.jsdelivr.net\/npm\/@flasher\/flasher@1.3.2\/dist\/flasher.min.js';    var FLASHER_FLASH_BAG_PLACE_HOLDER = {};    var options = mergeOptions([], FLASHER_FLASH_BAG_PLACE_HOLDER);    function mergeOptions(first, second) {        return {            context: merge(first.context || {}, second.context || {}),            envelopes: merge(first.envelopes || [], second.envelopes || []),            options: merge(first.options || {}, second.options || {}),            scripts: merge(first.scripts || [], second.scripts || []),            styles: merge(first.styles || [], second.styles || []),        };    }    function merge(first, second) {        if (Array.isArray(first) && Array.isArray(second)) {            return first.concat(second).filter(function(item, index, array) {                return array.indexOf(item) === index;            });        }        return Object.assign({}, first, second);    }    function renderOptions(options) {        if(!window.hasOwnProperty('flasher')) {            console.error('Flasher is not loaded');            return;        }        requestAnimationFrame(function () {            window.flasher.render(options);        });    }    function render(options) {        if ('loading' !== document.readyState) {            renderOptions(options);            return;        }        document.addEventListener('DOMContentLoaded', function() {            renderOptions(options);        });    }    if (1 === document.querySelectorAll('script.flasher-js').length) {        document.addEventListener('flasher:render', function (event) {            render(event.detail);        });            }    if (window.hasOwnProperty('flasher') || !rootScript || document.querySelector('script[src=\"' + rootScript + '\"]')) {        render(options);    } else {        var tag = document.createElement('script');        tag.setAttribute('src', rootScript);        tag.setAttribute('type', 'text\/javascript');        tag.onload = function () {            render(options);        };        document.head.appendChild(tag);    }})();<\/script>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f4f4f4;\">\r\n        <tbody><tr>\r\n            <td align=\"center\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;\">\r\n                            <h1 style=\"margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: 60px; width: auto; display: block; margin: 0 auto;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 30px 40px 20px; text-align: center;\">\r\n                            <h2 style=\"margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;\">Account Verification Requireded<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\">\r\n                            <p style=\"margin: 0; color: #6c757d; font-size: 16px;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 20px 40px; color: #333333;\">\r\n                            <p>Dear {{fullname}},<\/p><p>Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;\">\r\n                            <p style=\"margin: 0 0 10px 0; font-size: 16px;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: #6c757d; margin: 15px 0 0 0;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;\">\r\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: #ffffff; text-decoration: none;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: #ffffff; text-decoration: none;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: #ffffff; text-decoration: none;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 15px 0 0 0; font-size: 14px;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: #999999; margin: 10px 0 0 0;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: #999999; text-decoration: none;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>","template_id":"44","sms_body":"Account verification required. Please upload your documents in the KYC section of your dashboard."}  
[2025-06-30 15:54:54] production.INFO: Request Headers: {"host":["localhost"],"connection":["keep-alive"],"content-length":["20633"],"cache-control":["max-age=0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua-platform":["\"Windows\""],"origin":["https:\/\/localhost"],"content-type":["application\/x-www-form-urlencoded"],"upgrade-insecure-requests":["1"],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"sec-fetch-site":["same-origin"],"sec-fetch-mode":["navigate"],"sec-fetch-user":["?1"],"sec-fetch-dest":["document"],"referer":["https:\/\/localhost\/mbf.mybrokerforex.com-********\/admin\/notification\/template\/edit\/44"],"accept-encoding":["gzip, deflate, br, zstd"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"cookie":["XSRF-TOKEN=eyJpdiI6IjJzZW5ZSWZWRGt4c0FyQWxwZSs5T1E9PSIsInZhbHVlIjoiTzBBbSszSExKQzllTC96VVo1cno1NXVINXdyTEpBZEV6RW9TUFhnK2lFTXYyQW0rbjZXbHRPcnlMdHBSamhWWlk2UkR6SW1ERG4rYXZmeVhNWFpUelc5Ri9JdThzLzZ2OTMxYzZyODlkTVc1dGtBWWNSajNvWmk4dXRpakd5SGkiLCJtYWMiOiI5NjA4ZWRlMGQxY2Y5NzM2Zjk0ZmRlMDBjYzcwMDczMjc4YmRiZTU0YzBmMjUwMzkxMTM5ZTg0NGM1MzhkNDllIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InpXanF5RktwR0c5cVA3MlVjRjJmNFE9PSIsInZhbHVlIjoiY3pBWk83NzhaSk1kbk5EZ0RnSkVFVVdZb1NTR0t6QzFtcHZlenhMMUtkY0ppd2VHNFZDU1l1UjFUcmE5N2M5TWVRTXJlQzFBbWp1WUM5NmdCV2g4eDhLZmE3dGtxMHdFbTBLNWRuMmNnSHZzTTdxM0xyaDY2Z0dlcW1uQmd2bUsiLCJtYWMiOiJkYTc0MDQ2ZjI5MzViMDk1MWMzZjFkMjNlZDY3ODJkNGU2OThjMjMxNTFjMzE2NjRkMzBhZTBiNDdiODU4YzFiIiwidGFnIjoiIn0%3D"]}  
[2025-06-30 15:54:54] production.INFO: ✅ Validation passed  
[2025-06-30 15:54:54] production.INFO: ✅ Template found - Current subject: Account Verification Required - Action Needed  
[2025-06-30 15:54:54] production.INFO: ✅ Template found - Current email_body length: 7788  
[2025-06-30 15:54:54] production.INFO: ✅ Subject updated to: Account Verification Required - Action Needed  
[2025-06-30 15:54:54] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 15:54:54] production.INFO: Email body source: email_body_final  
[2025-06-30 15:54:54] production.INFO: Email body length: 7790  
[2025-06-30 15:54:54] production.INFO: Email body preview (first 200 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>

<script type="text/javascript" class="flasher-js  
[2025-06-30 15:54:54] production.INFO: Template 44: Using content directly from editor - NO PROCESSING  
[2025-06-30 15:54:54] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 15:54:54] production.INFO: Final email body length: 7790  
[2025-06-30 15:54:54] production.INFO: Final email body preview (first 300 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>

<script type="text/javascript" class="flasher-js">(function() {    var rootScript = 'https://cdn.jsdelivr.net/npm/@flasher/flasher@1.3.2/dist/flashe  
[2025-06-30 15:54:54] production.INFO: Template 44: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 15:54:54] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 15:54:54] production.INFO: Before save - Template email_body length: 7788  
[2025-06-30 15:54:54] production.INFO: Before save - New email_body length: 7790  
[2025-06-30 15:54:54] production.INFO: Before save - Template dirty: []  
[2025-06-30 15:54:54] production.INFO: After setting fields - Template dirty: {"email_body":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n<script type=\"text\/javascript\" class=\"flasher-js\">(function() {    var rootScript = 'https:\/\/cdn.jsdelivr.net\/npm\/@flasher\/flasher@1.3.2\/dist\/flasher.min.js';    var FLASHER_FLASH_BAG_PLACE_HOLDER = {};    var options = mergeOptions([], FLASHER_FLASH_BAG_PLACE_HOLDER);    function mergeOptions(first, second) {        return {            context: merge(first.context || {}, second.context || {}),            envelopes: merge(first.envelopes || [], second.envelopes || []),            options: merge(first.options || {}, second.options || {}),            scripts: merge(first.scripts || [], second.scripts || []),            styles: merge(first.styles || [], second.styles || []),        };    }    function merge(first, second) {        if (Array.isArray(first) && Array.isArray(second)) {            return first.concat(second).filter(function(item, index, array) {                return array.indexOf(item) === index;            });        }        return Object.assign({}, first, second);    }    function renderOptions(options) {        if(!window.hasOwnProperty('flasher')) {            console.error('Flasher is not loaded');            return;        }        requestAnimationFrame(function () {            window.flasher.render(options);        });    }    function render(options) {        if ('loading' !== document.readyState) {            renderOptions(options);            return;        }        document.addEventListener('DOMContentLoaded', function() {            renderOptions(options);        });    }    if (1 === document.querySelectorAll('script.flasher-js').length) {        document.addEventListener('flasher:render', function (event) {            render(event.detail);        });            }    if (window.hasOwnProperty('flasher') || !rootScript || document.querySelector('script[src=\"' + rootScript + '\"]')) {        render(options);    } else {        var tag = document.createElement('script');        tag.setAttribute('src', rootScript);        tag.setAttribute('type', 'text\/javascript');        tag.onload = function () {            render(options);        };        document.head.appendChild(tag);    }})();<\/script>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f4f4f4;\">\r\n        <tbody><tr>\r\n            <td align=\"center\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;\">\r\n                            <h1 style=\"margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: 60px; width: auto; display: block; margin: 0 auto;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 30px 40px 20px; text-align: center;\">\r\n                            <h2 style=\"margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;\">Account Verification Requireded<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\">\r\n                            <p style=\"margin: 0; color: #6c757d; font-size: 16px;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 20px 40px; color: #333333;\">\r\n                            <p>Dear {{fullname}},<\/p><p>Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;\">\r\n                            <p style=\"margin: 0 0 10px 0; font-size: 16px;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: #6c757d; margin: 15px 0 0 0;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;\">\r\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: #ffffff; text-decoration: none;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: #ffffff; text-decoration: none;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: #ffffff; text-decoration: none;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 15px 0 0 0; font-size: 14px;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: #999999; margin: 10px 0 0 0;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: #999999; text-decoration: none;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>"}  
[2025-06-30 15:54:54] production.INFO: After setting fields - Template email_body length: 7790  
[2025-06-30 15:54:54] production.INFO: Save operation result: SUCCESS  
[2025-06-30 15:54:54] production.INFO: After refresh - Template email_body length: 7790  
[2025-06-30 15:54:54] production.INFO: After refresh - Content matches: YES  
[2025-06-30 15:54:54] production.INFO: ✅ Template 44: Database operation completed  
[2025-06-30 15:54:54] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 17:34:01] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 17:34:01] production.INFO: Template ID: 44  
[2025-06-30 17:34:01] production.INFO: Request Method: POST  
[2025-06-30 17:34:01] production.INFO: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/44  
[2025-06-30 17:34:01] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 17:34:01] production.INFO: All Request Data: {"_token":"wXhpYNXjoV1G8hmyoOBJ5RaeqtV8Jn5rLg80GkJd","subject":"Account Verification Required - Action Needed","email_status":"on","email_body_final":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n        <tbody><tr>\r\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Required<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>","email_body":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n        <tbody><tr>\r\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Required<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>","template_id":"44","sms_body":"Account verification required. Please upload your documents in the KYC section of your dashboard."}  
[2025-06-30 17:34:01] production.INFO: Request Headers: {"host":["localhost"],"connection":["keep-alive"],"content-length":["19447"],"cache-control":["max-age=0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua-platform":["\"Windows\""],"origin":["https:\/\/localhost"],"content-type":["application\/x-www-form-urlencoded"],"upgrade-insecure-requests":["1"],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"sec-fetch-site":["same-origin"],"sec-fetch-mode":["navigate"],"sec-fetch-user":["?1"],"sec-fetch-dest":["document"],"referer":["https:\/\/localhost\/mbf.mybrokerforex.com-********\/admin\/notification\/template\/edit\/44"],"accept-encoding":["gzip, deflate, br, zstd"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"cookie":["XSRF-TOKEN=eyJpdiI6ImVwWmxjWVNSREVMYjJKckpyeVR5NXc9PSIsInZhbHVlIjoiV2o2OUxXYStCdW1Eem85YitQUFZJb1RodUY2VjJhOXg2bVBWMWFOam5jUVVnSlF4ZTl2ZmhyeGliQjJ4WXRPRElCMUJKZWhSR1kwaXdzT3VWSU5wN3NnOW9FVm81d2NiL2pzRzJrZzc1aGpaNGRxODg4N2JSQzRUdzRnbzJ0U04iLCJtYWMiOiI5ZTUyOGRlNDE3NDIyMDc3NjU0YmRjOTAyMTRkODMzNGUzOTA3OTA2MDgwYjQ1NGZkMWQzN2QyODBmYjg1NjA5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkkvZ2Z0dXI2aFpNREgvanpOS3JreEE9PSIsInZhbHVlIjoiRHMvUXBCUmtvODVuR0JkK3JNMVJ6ZVYrUjRleU1JV1YzWVVZYU14Sjh0TWhlOWdaVHhmaDg2a2JNalp1cU9vQTd5dXBzRXVpV1lWTWwzT2plcHVNUVcrTk1HTzNqem9HTzZyMHRRN1JnbTZiNlliRm9rUGh4UXNKSVpZd1dycGQiLCJtYWMiOiI5YWRhOWUxMGVhNDY1ZGY4MTAyMzQ2NmVmZmYyZDIwZGYxZjUyYWQ1Y2ZkNzdkOWNhNzk5NzNkMTU2OTQ3NDE2IiwidGFnIjoiIn0%3D"]}  
[2025-06-30 17:34:01] production.INFO: ✅ Validation passed  
[2025-06-30 17:34:01] production.INFO: ✅ Template found - Current subject: Account Verification Required - Action Needed  
[2025-06-30 17:34:01] production.INFO: ✅ Template found - Current email_body length: 7790  
[2025-06-30 17:34:01] production.INFO: ✅ Subject updated to: Account Verification Required - Action Needed  
[2025-06-30 17:34:01] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 17:34:01] production.INFO: Email body source: email_body_final  
[2025-06-30 17:34:01] production.INFO: Email body length: 7355  
[2025-06-30 17:34:01] production.INFO: Email body preview (first 200 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name  
[2025-06-30 17:34:01] production.INFO: Template 44: Using content directly from editor - NO PROCESSING  
[2025-06-30 17:34:01] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 17:34:01] production.INFO: Final email body length: 7355  
[2025-06-30 17:34:01] production.INFO: Final email body preview (first 300 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Requir  
[2025-06-30 17:34:01] production.INFO: Template 44: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 17:34:01] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 17:34:01] production.INFO: Before save - Template email_body length: 7790  
[2025-06-30 17:34:01] production.INFO: Before save - New email_body length: 7355  
[2025-06-30 17:34:01] production.INFO: Before save - Template dirty: []  
[2025-06-30 17:34:01] production.INFO: After setting fields - Template dirty: {"email_body":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n        <tbody><tr>\r\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Required<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>"}  
[2025-06-30 17:34:01] production.INFO: After setting fields - Template email_body length: 7355  
[2025-06-30 17:34:01] production.INFO: Save operation result: SUCCESS  
[2025-06-30 17:34:01] production.INFO: After refresh - Template email_body length: 7355  
[2025-06-30 17:34:01] production.INFO: After refresh - Content matches: YES  
[2025-06-30 17:34:01] production.INFO: ✅ Template 44: Database operation completed  
[2025-06-30 17:34:01] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 17:49:50] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 17:49:50] production.INFO: Template ID: 44  
[2025-06-30 17:49:50] production.INFO: Request Method: POST  
[2025-06-30 17:49:50] production.INFO: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/44  
[2025-06-30 17:49:50] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 17:49:50] production.INFO: All Request Data: {"_token":"wXhpYNXjoV1G8hmyoOBJ5RaeqtV8Jn5rLg80GkJd","subject":"Account Verification Required - Action Needed","email_status":"on","email_body_final":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n        <tbody><tr>\r\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Requireded<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>","email_body":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n        <tbody><tr>\r\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Requireded<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>","template_id":"44","sms_body":"Account verification required. Please upload your documents in the KYC section of your dashboard."}  
[2025-06-30 17:49:50] production.INFO: Request Headers: {"host":["localhost"],"connection":["keep-alive"],"content-length":["19451"],"cache-control":["max-age=0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua-platform":["\"Windows\""],"origin":["https:\/\/localhost"],"content-type":["application\/x-www-form-urlencoded"],"upgrade-insecure-requests":["1"],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"sec-fetch-site":["same-origin"],"sec-fetch-mode":["navigate"],"sec-fetch-user":["?1"],"sec-fetch-dest":["document"],"referer":["https:\/\/localhost\/mbf.mybrokerforex.com-********\/admin\/notification\/template\/edit\/44"],"accept-encoding":["gzip, deflate, br, zstd"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"cookie":["XSRF-TOKEN=eyJpdiI6ImsxakZkcmJER25IbE15dTl3Smp3ekE9PSIsInZhbHVlIjoiVlRML1JJcXBqcmVmSHBxUjZ3NHpVN3BqM0s0dWRJUDRxZFNuRThrMkk2QWx3RWpPVHFwWWFraVVPZ1YraXlBbnJtSEFuYnl1OHlSNTBHd1cwQ251WEQ5Q2oxbHk5ZjNuVW14dzhuaHArQUQraFhqV3A1R3NTRjZNd2Q5VEtlLzciLCJtYWMiOiJhZjk3NzFiNWRkYmY2Yjc4NWVkMDliNWMyYmZkMTc2ZmViODkzMzBlZGRhNDJlOGYzODNmMmFhOWQyMTNmNGViIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InJBc0krTm9uOWsybnBEaDJJWXhXV1E9PSIsInZhbHVlIjoiU1pUdkxTZ09yb1Y0dHZDQzBVWnhJMXlkeThGeHNVYnQwTDdPOUtQOGdkVTlIMTlGSVMyaHR1UUZLMy9XT1J2bXRtbXVXbjNhbnBndW5kaElmRFMvMzdLZUs5ckpRdXgrU2l0R0N1YlM4b2pzeUx4YWNqS1FwbHpoWnBWOHZYSjUiLCJtYWMiOiI2Y2U2ODE3YjgzNDViOTU3ZmIzYTdkOGZlNmI3MDhmNTA0NTRhNjI0ZDZkNzE4YjQ5MGQwNDUzYWY3MWQ4OTY4IiwidGFnIjoiIn0%3D"]}  
[2025-06-30 17:49:50] production.INFO: ✅ Validation passed  
[2025-06-30 17:49:50] production.INFO: ✅ Template found - Current subject: Account Verification Required - Action Needed  
[2025-06-30 17:49:50] production.INFO: ✅ Template found - Current email_body length: 7355  
[2025-06-30 17:49:50] production.INFO: ✅ Subject updated to: Account Verification Required - Action Needed  
[2025-06-30 17:49:50] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 17:49:50] production.INFO: Email body source: email_body_final  
[2025-06-30 17:49:50] production.INFO: Email body length: 7357  
[2025-06-30 17:49:50] production.INFO: Email body preview (first 200 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name  
[2025-06-30 17:49:50] production.INFO: Template 44: Using content directly from editor - NO PROCESSING  
[2025-06-30 17:49:50] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 17:49:50] production.INFO: Final email body length: 7357  
[2025-06-30 17:49:50] production.INFO: Final email body preview (first 300 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Requir  
[2025-06-30 17:49:50] production.INFO: Template 44: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 17:49:50] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 17:49:50] production.INFO: Before save - Template email_body length: 7355  
[2025-06-30 17:49:50] production.INFO: Before save - New email_body length: 7357  
[2025-06-30 17:49:50] production.INFO: Before save - Template dirty: []  
[2025-06-30 17:49:51] production.INFO: After setting fields - Template dirty: {"email_body":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n        <tbody><tr>\r\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Requireded<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>"}  
[2025-06-30 17:49:51] production.INFO: After setting fields - Template email_body length: 7357  
[2025-06-30 17:49:51] production.INFO: Save operation result: SUCCESS  
[2025-06-30 17:49:51] production.INFO: After refresh - Template email_body length: 7357  
[2025-06-30 17:49:51] production.INFO: After refresh - Content matches: YES  
[2025-06-30 17:49:51] production.INFO: ✅ Template 44: Database operation completed  
[2025-06-30 17:49:51] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 17:49:55] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 17:49:55] production.INFO: Template ID: 44  
[2025-06-30 17:49:55] production.INFO: Request Method: POST  
[2025-06-30 17:49:55] production.INFO: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/44  
[2025-06-30 17:49:55] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 17:49:55] production.INFO: All Request Data: {"_token":"wXhpYNXjoV1G8hmyoOBJ5RaeqtV8Jn5rLg80GkJd","subject":"Account Verification Required - Action Needed","email_status":"on","email_body_final":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n        <tbody><tr>\r\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Required<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>","email_body":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n        <tbody><tr>\r\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Required<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>","template_id":"44","sms_body":"Account verification required. Please upload your documents in the KYC section of your dashboard."}  
[2025-06-30 17:49:55] production.INFO: Request Headers: {"host":["localhost"],"connection":["keep-alive"],"content-length":["19447"],"cache-control":["max-age=0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua-platform":["\"Windows\""],"origin":["https:\/\/localhost"],"content-type":["application\/x-www-form-urlencoded"],"upgrade-insecure-requests":["1"],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"sec-fetch-site":["same-origin"],"sec-fetch-mode":["navigate"],"sec-fetch-user":["?1"],"sec-fetch-dest":["document"],"referer":["https:\/\/localhost\/mbf.mybrokerforex.com-********\/admin\/notification\/template\/edit\/44"],"accept-encoding":["gzip, deflate, br, zstd"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"cookie":["XSRF-TOKEN=eyJpdiI6IjNOSUVPUkhFeGg4VmswOHhBcWhtMVE9PSIsInZhbHVlIjoiSWFUZW1CZTNOMkQvRjdJN2ZIZ1VGTlVrR0hTallZSmNXMlRBalhFZmpHcFkxL09BYUNkSzk0UVZscWFwUmV3dXB0MnBMQjUyL3dwTWJic1NxM2lzTzJSYm9Zcit2UitQVUJuWEc3Vm5hVWpnR0VxTGZHQnRMNmRTZnNGNXhNd0kiLCJtYWMiOiIxMGVjYjFjYmE0ZGY4OGQ0NGI1NDhiMDU1ZDU3MDQ4Y2E0Y2FkYWQ2M2JlYzc4NGFmNWVmOWE0OTNjYTQ3MGJkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImJ1NUxZaXVhVW9qOGt5Zjk2eWN6T0E9PSIsInZhbHVlIjoiSlZaOEhVMXFZdFJJVGxHWjY1RjIvbFFicjhSTGZEak93TWpBOWM4d0tUZEhlOUJ0Q2ttK3V0NytsSFg2cWJoUzd5TmFPVXhuZk1SM1BQVG80TnZETkp0a20zbnRFemdFNG4wTWxQN2Z0TXFMalVkM2RoRjBEWUlyalB1UDRGRXUiLCJtYWMiOiI4MzkyN2QzN2Q4MTg3YTliNDZhYmNjM2QzNTE2NzVhYjQ2NTY4NDU3OGI2ZDZhZDQ0Y2FjMzhhYTkxNjZiM2I0IiwidGFnIjoiIn0%3D"]}  
[2025-06-30 17:49:55] production.INFO: ✅ Validation passed  
[2025-06-30 17:49:55] production.INFO: ✅ Template found - Current subject: Account Verification Required - Action Needed  
[2025-06-30 17:49:55] production.INFO: ✅ Template found - Current email_body length: 7357  
[2025-06-30 17:49:55] production.INFO: ✅ Subject updated to: Account Verification Required - Action Needed  
[2025-06-30 17:49:55] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 17:49:55] production.INFO: Email body source: email_body_final  
[2025-06-30 17:49:55] production.INFO: Email body length: 7355  
[2025-06-30 17:49:55] production.INFO: Email body preview (first 200 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name  
[2025-06-30 17:49:55] production.INFO: Template 44: Using content directly from editor - NO PROCESSING  
[2025-06-30 17:49:55] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 17:49:55] production.INFO: Final email body length: 7355  
[2025-06-30 17:49:55] production.INFO: Final email body preview (first 300 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Requir  
[2025-06-30 17:49:55] production.INFO: Template 44: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 17:49:55] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 17:49:55] production.INFO: Before save - Template email_body length: 7357  
[2025-06-30 17:49:55] production.INFO: Before save - New email_body length: 7355  
[2025-06-30 17:49:55] production.INFO: Before save - Template dirty: []  
[2025-06-30 17:49:55] production.INFO: After setting fields - Template dirty: {"email_body":"<meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Account Verification Required<\/title>\r\n\r\n\r\n    <!-- Full Width Container -->\r\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n        <tbody><tr>\r\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\r\n                <!-- Email Container -->\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\r\n\r\n                    <!-- Header Banner - Full Width -->\r\n                    <tbody><tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Logo Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Title Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Required<\/h2>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Description Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Main Content Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Regards Section -->\r\n                    <tr>\r\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\r\n                            <strong>MBFX Team<\/strong><\/p>\r\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                If you have any questions, please contact our support team.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                    <!-- Footer Section - Full Width -->\r\n                    <tr>\r\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\r\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\r\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\r\n                            <\/p>\r\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                \u00a9 2025 MBFX. All rights reserved.\r\n                            <\/p>\r\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\r\n                            <\/p>\r\n                        <\/td>\r\n                    <\/tr>\r\n\r\n                <\/tbody><\/table>\r\n            <\/td>\r\n        <\/tr>\r\n    <\/tbody><\/table>"}  
[2025-06-30 17:49:55] production.INFO: After setting fields - Template email_body length: 7355  
[2025-06-30 17:49:55] production.INFO: Save operation result: SUCCESS  
[2025-06-30 17:49:55] production.INFO: After refresh - Template email_body length: 7355  
[2025-06-30 17:49:55] production.INFO: After refresh - Content matches: YES  
[2025-06-30 17:49:55] production.INFO: ✅ Template 44: Database operation completed  
[2025-06-30 17:49:55] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 20:06:29] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 20:06:29] production.INFO: Template ID: 36  
[2025-06-30 20:06:29] production.INFO: Request Method: POST  
[2025-06-30 20:06:29] production.INFO: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/36  
[2025-06-30 20:06:29] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 20:06:29] production.INFO: Server Environment: WINNT - PHP 8.2.12  
[2025-06-30 20:06:29] production.INFO: Content Type: application/x-www-form-urlencoded  
[2025-06-30 20:06:29] production.INFO: Content Length: 18098  
[2025-06-30 20:06:29] production.INFO: Raw POST data keys: _token, subject, email_status, email_body_final, email_body, template_id, sms_body  
[2025-06-30 20:06:29] production.INFO: Email body field exists: YES  
[2025-06-30 20:06:29] production.INFO: Email body final field exists: YES  
[2025-06-30 20:06:29] production.INFO: ✅ Validation passed  
[2025-06-30 20:06:29] production.INFO: ✅ Template found - Current subject: IB Application Status Update  
[2025-06-30 20:06:29] production.INFO: ✅ Template found - Current email_body length: 5191  
[2025-06-30 20:06:29] production.INFO: ✅ Subject updated to: IB Application Status Update  
[2025-06-30 20:06:29] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 20:06:29] production.INFO: Email body source: email_body_final  
[2025-06-30 20:06:29] production.INFO: Email body length: 6882  
[2025-06-30 20:06:29] production.INFO: Email body preview (first 200 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IB Application Rejected</title>


    <!-- Full Width Container -->
    <table width=  
[2025-06-30 20:06:29] production.INFO: After minimal Windows cleanup - Email body length: 6802  
[2025-06-30 20:06:29] production.INFO: Template 36: Using content directly from editor with minimal cleanup  
[2025-06-30 20:06:29] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 20:06:29] production.INFO: Final email body length: 6802  
[2025-06-30 20:06:29] production.INFO: Final email body preview (first 300 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IB Application Rejected</title>


    <!-- Full Width Container -->
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: rgb(244, 244, 244); width: 100%  
[2025-06-30 20:06:29] production.INFO: Template 36: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 20:06:29] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 20:06:29] production.INFO: Before save - Template email_body length: 5191  
[2025-06-30 20:06:29] production.INFO: Before save - New email_body length: 6802  
[2025-06-30 20:06:29] production.INFO: Before save - Template dirty: []  
[2025-06-30 20:06:29] production.INFO: After setting fields - Template dirty: {"email_body":"<meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>IB Application Rejected<\/title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\n        <tbody><tr>\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\n                <!-- Email Container -->\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\n\n                    <!-- Header Banner - Full Width -->\n                    <tbody><tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">IB Application Update<\/h1>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Logo Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Title Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">IB Application Rejecteded<\/h2>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Description Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">We have reviewed your IB application.<\/p>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Main Content Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Thank you for your interest in our IB program. Unfortunately, your application could not be approved at this time.<\/p><ul><li><strong>Application Status:<\/strong> Rejected<\/li><li><strong>Reason:<\/strong> {{rejection_reason}}<\/li><li><strong>Review Date:<\/strong> {{review_date}}<\/li><\/ul><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">You may reapply after addressing the mentioned requirements.<\/p>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Regards Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\n                            <strong>MBFX Team<\/strong><\/p>\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                If you have any questions, please contact our support team.\n                            <\/p>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Footer Section - Full Width -->\n                    <tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\n                            <\/p>\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                \u00a9 2025 MBFX. All rights reserved.\n                            <\/p>\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\n                            <\/p>\n                        <\/td>\n                    <\/tr>\n\n                <\/tbody><\/table>\n            <\/td>\n        <\/tr>\n    <\/tbody><\/table>"}  
[2025-06-30 20:06:29] production.INFO: After setting fields - Template email_body length: 6802  
[2025-06-30 20:06:29] production.INFO: Save operation result: SUCCESS  
[2025-06-30 20:06:29] production.INFO: After refresh - Template email_body length: 6802  
[2025-06-30 20:06:29] production.INFO: After refresh - Content matches: YES  
[2025-06-30 20:06:29] production.INFO: ✅ Template 36: Database operation completed  
[2025-06-30 20:06:29] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 20:06:40] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 20:06:40] production.INFO: Template ID: 36  
[2025-06-30 20:06:40] production.INFO: Request Method: POST  
[2025-06-30 20:06:40] production.INFO: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/36  
[2025-06-30 20:06:40] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 20:06:40] production.INFO: Server Environment: WINNT - PHP 8.2.12  
[2025-06-30 20:06:40] production.INFO: Content Type: application/x-www-form-urlencoded  
[2025-06-30 20:06:40] production.INFO: Content Length: 18082  
[2025-06-30 20:06:40] production.INFO: Raw POST data keys: _token, subject, email_status, email_body_final, email_body, template_id, sms_body  
[2025-06-30 20:06:40] production.INFO: Email body field exists: YES  
[2025-06-30 20:06:40] production.INFO: Email body final field exists: YES  
[2025-06-30 20:06:40] production.INFO: ✅ Validation passed  
[2025-06-30 20:06:40] production.INFO: ✅ Template found - Current subject: IB Application Status Update  
[2025-06-30 20:06:40] production.INFO: ✅ Template found - Current email_body length: 6802  
[2025-06-30 20:06:40] production.INFO: ✅ Subject updated to: IB Application Status Update  
[2025-06-30 20:06:40] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 20:06:40] production.INFO: Email body source: email_body_final  
[2025-06-30 20:06:40] production.INFO: Email body length: 6878  
[2025-06-30 20:06:40] production.INFO: Email body preview (first 200 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IB Application Rejected</title>

    <!-- Full Width Container -->
    <table width="1  
[2025-06-30 20:06:40] production.INFO: After minimal Windows cleanup - Email body length: 6799  
[2025-06-30 20:06:40] production.INFO: Template 36: Using content directly from editor with minimal cleanup  
[2025-06-30 20:06:40] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 20:06:40] production.INFO: Final email body length: 6799  
[2025-06-30 20:06:40] production.INFO: Final email body preview (first 300 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IB Application Rejected</title>

    <!-- Full Width Container -->
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: rgb(244, 244, 244); width: 100%;  
[2025-06-30 20:06:40] production.INFO: Template 36: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 20:06:40] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 20:06:40] production.INFO: Before save - Template email_body length: 6802  
[2025-06-30 20:06:40] production.INFO: Before save - New email_body length: 6799  
[2025-06-30 20:06:40] production.INFO: Before save - Template dirty: []  
[2025-06-30 20:06:40] production.INFO: After setting fields - Template dirty: {"email_body":"<meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>IB Application Rejected<\/title>\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\n        <tbody><tr>\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\n                <!-- Email Container -->\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\n\n                    <!-- Header Banner - Full Width -->\n                    <tbody><tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">IB Application Update<\/h1>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Logo Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Title Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">IB Application Rejected<\/h2>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Description Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">We have reviewed your IB application.<\/p>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Main Content Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Thank you for your interest in our IB program. Unfortunately, your application could not be approved at this time.<\/p><ul><li><strong>Application Status:<\/strong> Rejected<\/li><li><strong>Reason:<\/strong> {{rejection_reason}}<\/li><li><strong>Review Date:<\/strong> {{review_date}}<\/li><\/ul><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">You may reapply after addressing the mentioned requirements.<\/p>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Regards Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\n                            <strong>MBFX Team<\/strong><\/p>\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                If you have any questions, please contact our support team.\n                            <\/p>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Footer Section - Full Width -->\n                    <tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\n                            <\/p>\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                \u00a9 2025 MBFX. All rights reserved.\n                            <\/p>\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\n                            <\/p>\n                        <\/td>\n                    <\/tr>\n\n                <\/tbody><\/table>\n            <\/td>\n        <\/tr>\n    <\/tbody><\/table>"}  
[2025-06-30 20:06:40] production.INFO: After setting fields - Template email_body length: 6799  
[2025-06-30 20:06:40] production.INFO: Save operation result: SUCCESS  
[2025-06-30 20:06:40] production.INFO: After refresh - Template email_body length: 6799  
[2025-06-30 20:06:40] production.INFO: After refresh - Content matches: YES  
[2025-06-30 20:06:40] production.INFO: ✅ Template 36: Database operation completed  
[2025-06-30 20:06:40] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 20:31:21] production.INFO: NotifyProcess: Template IB_APPLICATION_REJECTED content starts with: <meta charset="UTF-8">
    <meta name="viewport" c  
[2025-06-30 20:31:21] production.INFO: NotifyProcess: Using global template wrapping for IB_APPLICATION_REJECTED  
[2025-06-30 20:50:36] production.INFO: NotifyProcess: Template BAL_ADD content starts with: <!DOCTYPE html>
<html lang="en">
<head>
    <me  
[2025-06-30 20:50:36] production.INFO: NotifyProcess: Using professional template without global wrapping for BAL_ADD  
[2025-07-01 09:04:51] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-07-01 09:04:51] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-07-01 09:06:27] production.INFO: NotifyProcess: Template ACCOUNT_VERIFICATION_REQUIRED content starts with: <meta charset="UTF-8">
    <meta name="viewport"   
[2025-07-01 09:06:27] production.INFO: NotifyProcess: Using global template wrapping for ACCOUNT_VERIFICATION_REQUIRED  
[2025-07-01 09:08:11] production.INFO: 🔍 Open orders query for user: {"email":"<EMAIL>","sql":"select `mt5_orders_2025`.*, `mt5_users`.`Group` as `UserGroup`, `mt5_users`.`Login` as `UserLogin`, `mt5_users`.`Balance` as `AccountBalance` from `mt5_users` inner join `mt5_orders_2025` on `mt5_users`.`Login` = `mt5_orders_2025`.`Login` where `mt5_users`.`Email` = ? and `mt5_orders_2025`.`State` in (?, ?, ?, ?)"} 
[2025-07-01 09:08:22] production.INFO: API Account Data: {"user_email":"<EMAIL>","selected_login":"all","user_logins":[],"account_data":null} 
[2025-07-01 09:08:25] production.INFO: API Account Data: {"user_email":"<EMAIL>","selected_login":"all","user_logins":[],"account_data":null} 
[2025-07-01 09:08:35] production.INFO: API Account Data: {"user_email":"<EMAIL>","selected_login":"all","user_logins":[],"account_data":null} 
[2025-07-01 09:08:40] production.INFO: API Account Data: {"user_email":"<EMAIL>","selected_login":"all","user_logins":[],"account_data":null} 
[2025-07-01 09:08:43] production.INFO: API Account Data: {"user_email":"<EMAIL>","selected_login":"all","user_logins":[],"account_data":null} 
[2025-07-01 09:30:34] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-07-01 09:30:34] production.INFO: Template ID: 44  
[2025-07-01 09:30:34] production.INFO: Request Method: POST  
[2025-07-01 09:30:34] production.INFO: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/44  
[2025-07-01 09:30:34] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-07-01 09:30:34] production.INFO: Server Environment: WINNT - PHP 8.2.12  
[2025-07-01 09:30:34] production.INFO: Content Type: multipart/form-data; boundary=----WebKitFormBoundaryDvxlAJrepXOGHdj7  
[2025-07-01 09:30:34] production.INFO: Content Length: 25505  
[2025-07-01 09:30:34] production.INFO: Raw POST data keys: _token, template_id, subject, email_status, sms_status, sms_body, email_body_encoded, email_body, email_body_final  
[2025-07-01 09:30:34] production.INFO: Email body field exists: YES  
[2025-07-01 09:30:34] production.INFO: Email body final field exists: YES  
[2025-07-01 09:30:34] production.INFO: ✅ Validation passed  
[2025-07-01 09:30:34] production.INFO: ✅ Template found - Current subject: Account Verification Required - Action Needed  
[2025-07-01 09:30:34] production.INFO: ✅ Template found - Current email_body length: 7355  
[2025-07-01 09:30:34] production.INFO: ✅ Subject updated to: Account Verification Required - Action Needed  
[2025-07-01 09:30:34] production.INFO: === ENHANCED EMAIL BODY PROCESSING WITH BASE64 SUPPORT ===  
[2025-07-01 09:30:34] production.INFO: 📦 Base64 encoded content detected  
[2025-07-01 09:30:34] production.INFO: ✅ Base64 content decoded successfully, length: 7260  
[2025-07-01 09:30:34] production.INFO: 📝 Decoded content preview: <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name="viewpo  
[2025-07-01 09:30:34] production.INFO: Email body source: email_body_final  
[2025-07-01 09:30:34] production.INFO: Email body length: 7260  
[2025-07-01 09:30:34] production.INFO: Email body preview (first 200 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name="viewpo  
[2025-07-01 09:30:34] production.INFO: After minimal Windows cleanup - Email body length: 7260  
[2025-07-01 09:30:34] production.INFO: Template 44: Using content directly from editor with minimal cleanup  
[2025-07-01 09:30:34] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-07-01 09:30:34] production.INFO: Final email body length: 7260  
[2025-07-01 09:30:34] production.INFO: Final email body preview (first 300 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title  
[2025-07-01 09:30:34] production.INFO: Template 44: Skipping all corruption detection and complex processing to prevent issues  
[2025-07-01 09:30:34] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-07-01 09:30:34] production.INFO: Before save - Template email_body length: 7355  
[2025-07-01 09:30:34] production.INFO: Before save - New email_body length: 7260  
[2025-07-01 09:30:34] production.INFO: Before save - Template dirty: []  
[2025-07-01 09:30:34] production.INFO: After setting fields - Template dirty: {"email_body":"<meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required<\/title>\n\n\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required<\/title>\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required<\/title>\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required<\/title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\n        <tbody><tr>\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\n                <!-- Email Container -->\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\n\n                    <!-- Header Banner - Full Width -->\n                    <tbody><tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Logo Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Title Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Requireded<\/h2>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Description Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Main Content Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Regards Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\n                            <strong>MBFX Team<\/strong><\/p>\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                If you have any questions, please contact our support team.\n                            <\/p>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Footer Section - Full Width -->\n                    <tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\n                            <\/p>\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                \u00a9 2025 MBFX. All rights reserved.\n                            <\/p>\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\n                            <\/p>\n                        <\/td>\n                    <\/tr>\n\n                <\/tbody><\/table>\n            <\/td>\n        <\/tr>\n    <\/tbody><\/table>"}  
[2025-07-01 09:30:34] production.INFO: After setting fields - Template email_body length: 7260  
[2025-07-01 09:30:34] production.INFO: Save operation result: SUCCESS  
[2025-07-01 09:30:34] production.INFO: After refresh - Template email_body length: 7260  
[2025-07-01 09:30:34] production.INFO: After refresh - Content matches: YES  
[2025-07-01 09:30:34] production.INFO: ✅ Template 44: Database operation completed  
[2025-07-01 09:30:34] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-07-01 09:30:34] production.INFO: 📤 Returning AJAX response  
[2025-07-01 09:30:41] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-07-01 09:30:41] production.INFO: Template ID: 44  
[2025-07-01 09:30:41] production.INFO: Request Method: POST  
[2025-07-01 09:30:41] production.INFO: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/44  
[2025-07-01 09:30:41] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-07-01 09:30:41] production.INFO: Server Environment: WINNT - PHP 8.2.12  
[2025-07-01 09:30:41] production.INFO: Content Type: multipart/form-data; boundary=----WebKitFormBoundarywxRUYiQOxdhbLDHv  
[2025-07-01 09:30:41] production.INFO: Content Length: 25501  
[2025-07-01 09:30:41] production.INFO: Raw POST data keys: _token, template_id, subject, email_status, sms_status, sms_body, email_body_encoded, email_body, email_body_final  
[2025-07-01 09:30:41] production.INFO: Email body field exists: YES  
[2025-07-01 09:30:41] production.INFO: Email body final field exists: YES  
[2025-07-01 09:30:41] production.INFO: ✅ Validation passed  
[2025-07-01 09:30:41] production.INFO: ✅ Template found - Current subject: Account Verification Required - Action Needed  
[2025-07-01 09:30:41] production.INFO: ✅ Template found - Current email_body length: 7260  
[2025-07-01 09:30:41] production.INFO: ✅ Subject updated to: Account Verification Required - Action Needed  
[2025-07-01 09:30:41] production.INFO: === ENHANCED EMAIL BODY PROCESSING WITH BASE64 SUPPORT ===  
[2025-07-01 09:30:41] production.INFO: 📦 Base64 encoded content detected  
[2025-07-01 09:30:41] production.INFO: ✅ Base64 content decoded successfully, length: 7258  
[2025-07-01 09:30:41] production.INFO: 📝 Decoded content preview: <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name="viewpo  
[2025-07-01 09:30:41] production.INFO: Email body source: email_body_final  
[2025-07-01 09:30:41] production.INFO: Email body length: 7258  
[2025-07-01 09:30:41] production.INFO: Email body preview (first 200 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name="viewpo  
[2025-07-01 09:30:41] production.INFO: After minimal Windows cleanup - Email body length: 7258  
[2025-07-01 09:30:41] production.INFO: Template 44: Using content directly from editor with minimal cleanup  
[2025-07-01 09:30:41] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-07-01 09:30:41] production.INFO: Final email body length: 7258  
[2025-07-01 09:30:41] production.INFO: Final email body preview (first 300 chars): <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title>




    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification Required</title  
[2025-07-01 09:30:41] production.INFO: Template 44: Skipping all corruption detection and complex processing to prevent issues  
[2025-07-01 09:30:41] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-07-01 09:30:41] production.INFO: Before save - Template email_body length: 7260  
[2025-07-01 09:30:41] production.INFO: Before save - New email_body length: 7258  
[2025-07-01 09:30:41] production.INFO: Before save - Template dirty: []  
[2025-07-01 09:30:41] production.INFO: After setting fields - Template dirty: {"email_body":"<meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required<\/title>\n\n\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required<\/title>\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required<\/title>\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required<\/title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\n        <tbody><tr>\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\n                <!-- Email Container -->\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\n\n                    <!-- Header Banner - Full Width -->\n                    <tbody><tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification<\/h1>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Logo Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <img src=\"https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Title Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Required<\/h2>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Description Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.<\/p>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Main Content Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},<\/p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.<\/p><ul><li><strong>Account Status:<\/strong> Verification Required<\/li><li><strong>Required Actions:<\/strong> Complete KYC verification<\/li><li><br><\/li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.<\/span><\/li><\/ul>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Regards Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\n                            <strong>MBFX Team<\/strong><\/p>\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                If you have any questions, please contact our support team.\n                            <\/p>\n                        <\/td>\n                    <\/tr>\n\n                    <!-- Footer Section - Full Width -->\n                    <tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX<\/strong> - Professional Trading Platform<\/p>\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings<\/a> |\n                                <a href=\"{{site_url}}\/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support<\/a> |\n                                <a href=\"{{site_url}}\/policy\/privacy-policy\/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy<\/a>\n                            <\/p>\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                \u00a9 2025 MBFX. All rights reserved.\n                            <\/p>\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\n                                <a href=\"{{site_url}}\/user\/profile\/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences<\/a>.\n                            <\/p>\n                        <\/td>\n                    <\/tr>\n\n                <\/tbody><\/table>\n            <\/td>\n        <\/tr>\n    <\/tbody><\/table>"}  
[2025-07-01 09:30:41] production.INFO: After setting fields - Template email_body length: 7258  
[2025-07-01 09:30:41] production.INFO: Save operation result: SUCCESS  
[2025-07-01 09:30:41] production.INFO: After refresh - Template email_body length: 7258  
[2025-07-01 09:30:41] production.INFO: After refresh - Content matches: YES  
[2025-07-01 09:30:41] production.INFO: ✅ Template 44: Database operation completed  
[2025-07-01 09:30:41] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-07-01 09:30:41] production.INFO: 📤 Returning AJAX response  
[2025-07-01 09:30:44] production.INFO: NotifyProcess: Template ACCOUNT_VERIFICATION_REQUIRED content starts with: <meta charset="UTF-8">
    <meta name="viewport" c  
[2025-07-01 09:30:44] production.INFO: NotifyProcess: Using global template wrapping for ACCOUNT_VERIFICATION_REQUIRED  
[2025-07-01 09:41:38] production.INFO: NotifyProcess: Template BAL_ADD content starts with: <!DOCTYPE html>
<html lang="en">
<head>
    <me  
[2025-07-01 09:41:38] production.INFO: NotifyProcess: Using professional template without global wrapping for BAL_ADD  
[2025-07-01 09:42:15] production.INFO: NotifyProcess: Template BAL_ADD content starts with: <!DOCTYPE html>
<html lang="en">
<head>
    <me  
[2025-07-01 09:42:15] production.INFO: NotifyProcess: Using professional template without global wrapping for BAL_ADD  
[2025-07-01 09:52:18] production.INFO: NotifyProcess: Template BAL_ADD content starts with: <!DOCTYPE html>
<html lang="en">
<head>
    <me  
[2025-07-01 09:52:18] production.INFO: NotifyProcess: Using professional template without global wrapping for BAL_ADD  
[2025-07-01 09:54:08] production.INFO: NotifyProcess: Template BAL_ADD content starts with: <!DOCTYPE html>
<html lang="en">
<head>
    <me  
[2025-07-01 09:54:08] production.INFO: NotifyProcess: Using professional template without global wrapping for BAL_ADD  
